﻿namespace Robot;

public partial class FormInputBox : Form
{
    public string Value { get; set; } = string.Empty;

    public FormInputBox()
    {
        InitializeComponent();
    }

    public FormInputBox(string title)
    {
        InitializeComponent();
        Text = title;
    }

    public FormInputBox(string title, string label)
    {
        InitializeComponent();
        Text = title;
        label_Tips.Text = label;
    }

    public FormInputBox(string title, string label, string value)
    {
        InitializeComponent();
        Text = title;
        label_Tips.Text = label;
        Value = value;
    }

    private void FormInputBox_Load(object sender, EventArgs e)
    {
        textBox_Value.Focus();
        textBox_Value.Text = Value;
    }

    private void button_Sure_Click(object sender, EventArgs e)
    {
        DialogResult = DialogResult.OK;
        Value = textBox_Value.Text;
        Close();
    }

    private void button_Cancel_Click(object sender, EventArgs e)
    {
        DialogResult = DialogResult.Cancel;
        Close();
    }
}