using AiHelper;
using Flurl.Http;
using Robot.ChatPlatform;
using Robot.Config;
using Robot.Enum;
using Robot.Helper;
using Robot.Models;
using Robot.Services;
using Sunny.UI;

namespace Robot;

public partial class FormMain : Form
{
    #region 构造函数

    /// <summary>
    /// 构造函数
    /// </summary>
    public FormMain()
    {
        InitializeComponent();
        CommonHelper.Context = SynchronizationContext.Current!;
        CommonHelper.ServiceParamDic.TryAdd(CommonHelper.IsStartService, false);
    }

    #endregion

    #region 载入主窗体

    /// <summary>
    /// 载入主窗体
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void FormMain_Load(object sender, EventArgs e)
    {
        try
        {
            // 最小化到托盘
            // WindowState = FormWindowState.Minimized;
            // this.Hide();

            // 启动日志
            await DbHelper.AddLog(EnumLogType.机器人, "Robot启动", "Robot启动");
            if (CommonHelper.Lottery == EnumLottery.台湾宾果)
            {
                await DbHelper.AddLog(EnumLogType.平台, "当前游戏", "台湾宾果");
            }
            else if (CommonHelper.Lottery == EnumLottery.一六八飞艇)
            {
                await DbHelper.AddLog(EnumLogType.平台, "当前游戏", "168飞艇");
            }
            else if (CommonHelper.Lottery == EnumLottery.新一六八XL)
            {
                await DbHelper.AddLog(EnumLogType.平台, "当前游戏", "新168XL");
            }

            // 判断是否移除一起聊吧选项
            if (CommonHelper.ChatApp != EnumChatApp.一起聊吧 && CommonHelper.ChatApp != EnumChatApp.VoceChat)
            {
                tabControl_Main.TabPages.Remove(tabPage_OneChat);
            }

            // 初始化数据库
            DbHelper.InitTable();
            DbHelper.InitOdds();

            // 启动时清空开奖信息
            await DbHelper.FSql.Delete<KaiJiang>()
                .Where(a => a.Id > 0)
                .ExecuteAffrowsAsync();

            // 启动时创建期号时间信息
            await IssueTimeHelper.CreateIssueTimeAsync(CommonHelper.Cts.Token);

            // 初始化声音服务
            SoundHelper.InitSoundService();

            // 初始化UI
            await InitUi();

            // 初始化浏览器
            await InitializeWebView();

            // 启动监听
            if (CommonHelper.ChatApp != EnumChatApp.微信391125)
            {
                _ = Task.Run(HttpListenerHelper.ListeningAsync);
                _ = Task.Run(HttpListenerHelper.ProcessMessageQueueAsync);
            }

            // 启动服务
            await Task.WhenAll(
                IssueTimeHelper.PreIssueTimeAsync(CommonHelper.Cts.Token),
                ServiceAsync(CommonHelper.Cts.Token)
            );
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "Robot启动异常", ex.ToString());
        }
    }

    #endregion

    #region 初始化浏览器

    /// <summary>
    /// 初始化浏览器
    /// </summary>
    /// <returns></returns>
    private async Task InitializeWebView()
    {
        try
        {
            await webView2_OneChat.EnsureCoreWebView2Async(null);
            if (CommonHelper.ChatApp == EnumChatApp.一起聊吧)
            {
                webView2_OneChat.CoreWebView2.Navigate("http://127.0.0.1:3001/admin.html");
            }
            else if (CommonHelper.ChatApp == EnumChatApp.VoceChat)
            {
                webView2_OneChat.CoreWebView2.Navigate(VoiceChat.Current.Host);
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "InitializeWebViewError", ex.ToString());
        }
    }

    #endregion

    #region 服务主线程

    /// <summary>
    /// 服务主线程
    /// </summary>
    private async Task ServiceAsync(CancellationToken token)
    {
        long whileCount = 0;
        while (!token.IsCancellationRequested)
        {
            await Task.Delay(1000, token);
            whileCount++;

            try
            {
                // 每1秒刷新基础信息
                if (whileCount % UiConstants.RefreshIntervalBasic == 0)
                {
                    await Task.Run(ShowInfoAsync, token);
                    await Task.Run(ShowAddMoneyOrderAsync, token);
                    await Task.Run(ShowSubMoneyOrderAsync, token);
                    await Task.Run(ShowPlatformLogAsync, token);
                }

                // 每2秒刷新一次订单
                if (whileCount % UiConstants.RefreshIntervalOrder == 0)
                {
                    await Task.Run(ShowBetOrderAsync, token);
                }

                // 每3秒刷新一次会员信息
                if (whileCount % UiConstants.RefreshIntervalMemberInfo == 0)
                {
                    await Task.Run(ShowMemberInfoAsync, token);
                }

                // 判断获取消息列表,WeChat391125需要独立读取消息,把读取消息放在这里，先将旧的消息读取完毕
                if (CommonHelper.ChatApp == EnumChatApp.微信391125)
                {
                    await ChatHelper.GetMsgList();
                }

                // 判断获取Robot信息
                if (string.IsNullOrEmpty(RobotHelper.RobotInfo.Account))
                {
                    // 微信360018版本使用千寻微信框架,通过获取微信列表信息获取Robot信息
                    if (CommonHelper.ChatApp == EnumChatApp.微信360018)
                    {
                        await WeChatHelper360018.GetWeChatList();
                    }
                    else
                    {
                        await ChatHelper.GetRobotInfo();
                    }

                    continue;
                }

                // 判断获取群列表
                if (RobotHelper.GroupDic.Count.Equals(0))
                {
                    await ChatHelper.GetGroupDic();
                    continue;
                }

                // 把游戏种类传递给平台端,平台端根据游戏种类获取对应开奖信息
                try
                {
                    string url = $"{Setting.Current.PlatformHost}/GameInfo/";
                    await url.WithTimeout(TimeSpan.FromSeconds(3))
                        .PostJsonAsync(new
                        {
                            CommonHelper.Lottery
                        }, cancellationToken: token);
                }
                catch (Exception ex)
                {
                    await DbHelper.AddLog(EnumLogType.机器人, "传递游戏种类", ex.ToString());
                }

                // 获取平台最新额度信息
                try
                {
                    string url = $"{Setting.Current.PlatformHost}/UserInfo/";
                    UserInfo? tmpUserInfo = await url.WithTimeout(TimeSpan.FromSeconds(3)).PostAsync(cancellationToken: token).ReceiveJson<UserInfo>();
                    if (tmpUserInfo != null)
                    {
                        if (CommonHelper.UserInfo != null && !CommonHelper.UserInfo.Balance.Equals(tmpUserInfo.Balance))
                        {
                            CommonHelper.UserInfo = tmpUserInfo;
                            await DbHelper.AddLog(EnumLogType.平台, "额度变动", $"最新额度:{Ai.中括号左}{CommonHelper.UserInfo.Balance}{Ai.中括号右}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    await DbHelper.AddLog(EnumLogType.机器人, "获取平台额度信息异常", ex.ToString());
                }

                // 处理假人自动上分
                if (Setting.Current.假人自动上分)
                {
                    await JiaRenAutoAddMoneyHandler();
                }

                // 处理开奖
                await DrawService.ProcessDrawInfo(token);

                // 判断开始状态
                if (!CommonHelper.ServiceParamDic[CommonHelper.IsStartService])
                {
                    // 把未读消息标记为已读
                    await DbHelper.FSql.Update<ReceiveMessage>()
                        .Set(a => a.IsRead, true)
                        .Set(a => a.PreTime, DateTime.Now)
                        .Where(a => a.IsRead == false)
                        .ExecuteAffrowsAsync(token);

                    // 跳到下一轮循环
                    continue;
                }

                // 提取未读消息
                List<ReceiveMessage> msgList = await DbHelper.FSql.Select<ReceiveMessage>()
                    .Where(a => a.IsRead == false)
                    .Where(a => a.ReceiveTime > CommonHelper.StartServiceTime)
                    .ToListAsync(token);

                // 记录处理消息条数
                if (msgList.Count > 0)
                {
                    await DbHelper.AddLog(EnumLogType.机器人, "处理未读消息", $"共有{msgList.Count}条未读消息,{msgList[0].Id}-{Enumerable.Last(msgList).Id}");
                }

                // 遍历未读消息并处理
                foreach (ReceiveMessage msg in msgList)
                {
                    // 更新数据库
                    await DbHelper.FSql.Update<ReceiveMessage>()
                        .Set(a => a.IsRead, true)
                        .Set(a => a.PreTime, DateTime.Now)
                        .Where(a => a.Id == msg.Id)
                        .ExecuteAffrowsAsync(token);

                    // 处理消息
                    await RobotHelper.ReceiveMessageHandlerAsync(msg);
                }

                // 判断开盘提醒
                if (!RobotHelper.OpenTipsList.Contains(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue)
                    && IssueTimeHelper.CloseTimeDownDic[CommonHelper.Lottery] <= Setting.Current.开盘时间
                    && IssueTimeHelper.CloseTimeDownDic[CommonHelper.Lottery] > Setting.Current.封盘时间)
                {
                    // 取出当前期的上一期期号信息
                    IssueTime lastIssue = await DbHelper.FSql.Select<IssueTime>()
                        .Where(a => a.Id.Equals(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Id - 1))
                        .ToOneAsync(token);

                    // 取出上一期开奖数据
                    KaiJiang lastIssueDraw = await DbHelper.FSql.Select<KaiJiang>()
                        .Where(a => a.Issue == lastIssue.Issue)
                        .ToOneAsync(token);

                    // 若上一期开奖数据不存在,则进入下一轮循环
                    if (lastIssueDraw == null)
                    {
                        continue;
                    }

                    await Task.Delay(500, token);
                    RobotHelper.OpenTipsList.Add(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue);
                    await DbHelper.AddLog(EnumLogType.机器人, Ai.中括号左 + "开始接受答题" + Ai.中括号右, string.Concat(Ai.中括号左, IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue, Ai.中括号右, "期开始接受答题"));
                    await RobotHelper.发送开盘提醒(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue);

                    // 发送开盘提示音
                    SoundHelper.MediaOpenDraw.Play();
                }

                // 判断封盘倒计时提醒
                if (RobotHelper.OpenTipsList.Contains(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue)
                    && !RobotHelper.CloseDownTipsList.Contains(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue)
                    && IssueTimeHelper.CloseTimeDownDic[CommonHelper.Lottery] <= Setting.Current.封盘时间 + 30)
                {
                    RobotHelper.CloseDownTipsList.Add(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue);
                    await DbHelper.AddLog(EnumLogType.机器人, Ai.中括号左 + "封盘时间提醒" + Ai.中括号右, string.Concat(Ai.中括号左, IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue, Ai.中括号右, "封卷时间还剩", (IssueTimeHelper.CloseTimeDownDic[CommonHelper.Lottery] - Setting.Current.封盘时间 + 1).ToString(), "秒"));
                    await RobotHelper.发送封盘倒计时提醒(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue);
                }

                // 判断封盘处理
                if (RobotHelper.OpenTipsList.Contains(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue)
                    && !RobotHelper.CloseTipsList.Contains(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue)
                    && IssueTimeHelper.CloseTimeDownDic[CommonHelper.Lottery] <= Setting.Current.封盘时间)
                {
                    // 停止答题
                    RobotHelper.CloseTipsList.Add(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue);
                    await DbHelper.AddLog(EnumLogType.机器人, Ai.中括号左 + "停止答题提醒" + Ai.中括号右, string.Concat(Ai.中括号左, IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue, Ai.中括号右, "停止答题", Ai.中括号左, IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue, Ai.中括号右));

                    // 记录日志
                    await DbHelper.AddLog(EnumLogType.平台, "停止答题", $"封盘设置:{Ai.中括号左}{Setting.Current.封盘时间}{Ai.中括号右},倒计时余:{Ai.中括号左}{IssueTimeHelper.CloseTimeDownDic[CommonHelper.Lottery]}{Ai.中括号右}");

                    // 实时提取所有Member
                    List<Member> memberList = await DbHelper.FSql.Select<Member>().ToListAsync(token);

                    // 提取本期所有已受理的下注数据
                    List<BetOrder>? betOrderList = await DbHelper.FSql.Select<BetOrder>()
                        .Where(a => a.Issue == IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue)
                        .Where(a => a.BetOrderStatus == EnumBetOrderStatus.已受理)
                        .ToListAsync(token);

                    // 根据用户真假状态变更注单状态
                    foreach (BetOrder betOrder in betOrderList)
                    {
                        betOrder.OrderType = memberList.First(a => a.Account == betOrder.Account).是否假人 ? EnumOrderType.假人订单 : EnumOrderType.真人订单;

                        // 更新数据库
                        await DbHelper.FSql.Update<BetOrder>()
                            .Set(a => a.OrderType, betOrder.OrderType)
                            .Where(a => a.Id == betOrder.Id)
                            .ExecuteAffrowsAsync(token);
                    }

                    // 发送封盘数据,并处理汇总数据,并处理对冲吃单
                    await RobotHelper.发送封盘提醒(betOrderList);
                    await RobotHelper.处理汇总数据(betOrderList);
                    await RobotHelper.处理对冲吃单();

                    // 没有开启飞单则直接播放封盘提示音
                    if (!Setting.Current.是否开启飞单)
                    {
                        await DbHelper.AddLog(EnumLogType.平台, "飞单提醒", $"{Ai.中括号左}{IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue}{Ai.中括号右}期未开启飞单.");
                        SoundHelper.MediaStopBet.Play();
                        continue;
                    }

                    // 从汇总表判断有无飞单数据
                    var count = await DbHelper.FSql.Select<HuiZong>()
                        .Where(a => a.Issue == IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue)
                        .Where(a => a.ToBetBalance > 0)
                        .Where(a => a.BetResult == EnumBetResult.未知)
                        .CountAsync(token);

                    // 没有飞单数据则直接播放封盘提示音
                    if (count == 0)
                    {
                        await DbHelper.AddLog(EnumLogType.平台, "飞单提醒", $"{Ai.中括号左}{IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue}{Ai.中括号右}期没有飞单数据.");
                        SoundHelper.MediaStopBet.Play();
                        continue;
                    }

                    // 处理飞单
                    Setting.Current.LastBetIssue = IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue;
                    await BetHelper.AutoBet(); // 执行自动飞单
                    await BetHelper.CheckBetResult(); // 如果成功则记录成功状态,否则保留未知状态
                    await BetHelper.ShowBetResultFailed(); // 未知状态均为失败,弹出提示框

                    // 判断是否需要自动撤销
                    if (Setting.Current.AutoCancelOrder)
                    {
                        await BetHelper.AutoCancelBetFailedData();
                    }
                }
            }
            catch (Exception ex)
            {
                await DbHelper.AddLog(EnumLogType.机器人, "ServiceError", ex.ToString());
            }
        }
    }

    #endregion

    #region 关闭主窗口

    /// <summary>
    /// 关闭主窗口
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void FormMain_FormClosing(object sender, FormClosingEventArgs e)
    {
        if (!UIMessageBox.ShowMessageDialog("确定要关闭程序吗？", "操作提示", true, UIStyle.Red, true))
        {
            e.Cancel = true;
            return;
        }

        CommonHelper.Cts.Cancel();
        HttpListenerHelper.Cts.Cancel();
        HttpListenerHelper.Listener.Stop();
        HttpListenerHelper.Listener.Close();
    }

    #endregion
}