﻿using AiHelper;
using Flurl.Http;
using Newtonsoft.Json.Linq;
using Robot.ChatPlatform;
using Robot.Config;
using Robot.Enum;
using Robot.Models;

namespace Robot.Helper;

public static class BetHelper
{
    /// <summary>
    /// AutoBet
    /// </summary>
    public static async Task AutoBet()
    {
        try
        {
            // 先将原有未知结果的记录标记为失败
            await DbHelper.FSql.Update<BetResult>()
                .Set(a => a.Result, EnumBetResult.失败)
                .Where(a => a.Result == EnumBetResult.未知)
                .ExecuteAffrowsAsync();

            // 从汇总表提取出最后一期所有待飞单数据
            var toBetList = await DbHelper.FSql.Select<HuiZong>()
                .Where(a => a.Issue == Setting.Current.LastBetIssue)
                .Where(a => a.ToBetBalance > 0)
                .Where(a => a.BetResult == EnumBetResult.未知)
                .ToListAsync(a => new
                {
                    a.Id,
                    a.BetLottery,
                    a.Issue,
                    a.Content,
                    a.ToBetBalance
                });

            // 提取游戏种类
            HashSet<EnumBetLottery> betLotteryList = [.. toBetList.Select(betOrder => betOrder.BetLottery)];
            betLotteryList = [.. betLotteryList.OrderBy(enumBetLottery => (int)enumBetLottery)];
            await DbHelper.AddLog(EnumLogType.平台, "飞单游戏", string.Join(",", betLotteryList));

            // 遍历游戏种类
            foreach (EnumBetLottery betLottery in betLotteryList)
            {
                // 过滤游戏种类
                if (toBetList.All(a => a.BetLottery != betLottery))
                {
                    continue;
                }

                // 拼接飞单数据,用于显示到右下角日志
                var postBetList = toBetList.Where(a => a.BetLottery == betLottery).ToList();
                string betContent = Ai.中括号左 + toBetList[0].Issue + Ai.中括号右 + string.Join("", postBetList.Select(bet => Ai.中括号左 + bet.Content + "-" + bet.ToBetBalance + Ai.中括号右));

                // 记录日志
                await DbHelper.AddLog(EnumLogType.平台, "飞单内容", $"{Ai.中括号左}{betLottery}{Ai.中括号右}" + betContent);

                // 记录游戏种类,用于后续判断
                BetResult betResult = new BetResult
                {
                    BetLottery = betLottery,
                    Issue = IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue
                };
                await DbHelper.FSql.Insert<BetResult>()
                    .AppendData(betResult)
                    .ExecuteAffrowsAsync();

                // 发送飞单数据,不设置超时时间
                string url = $"{Setting.Current.PlatformHost}/ToBet/";
                string responseContent;
                try
                {
                    responseContent = await url
                        .WithTimeout(TimeSpan.FromSeconds(7))
                        .PostJsonAsync(postBetList)
                        .ReceiveString();
                }
                catch (Exception ex)
                {
                    responseContent = ex.ToString();
                }

                await DbHelper.AddLog(EnumLogType.平台, "飞单反馈", $@"{responseContent}");

                // 判断是否投注成功
                bool isSuccess = responseContent.Contains("成功");

                // 飞单成功
                if (isSuccess)
                {
                    // 更新汇总表
                    await DbHelper.FSql.Update<HuiZong>()
                        .Set(a => a.BetResult, EnumBetResult.成功)
                        .Where(a => a.Issue == IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue)
                        .ExecuteAffrowsAsync();

                    // 更新游戏种类结果
                    await DbHelper.FSql.Update<BetResult>()
                        .Set(a => a.Result, EnumBetResult.成功)
                        .Where(a => a.BetLottery == betResult.BetLottery)
                        .Where(a => a.Issue == betResult.Issue)
                        .ExecuteAffrowsAsync();

                    // 记录日志
                    await DbHelper.AddLog(EnumLogType.平台, "飞单成功", $@"{Ai.中括号左}{betLottery}@{IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue}{Ai.中括号右}飞单成功.");

                    // 弹出提示框
                    // 在非 UI 线程中调用
                    CommonHelper.Context.Post(_ =>
                    {
                        FormAlertMessage frm = new FormAlertMessage();
                        frm.Show($@"{Ai.中括号左}{betLottery}@{IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue}{Ai.中括号右}飞单成功.", AlertType.Success);
                    }, null);

                    // Invoke(() =>
                    // {
                    //     FormAlertMessage frm = new FormAlertMessage();
                    //     frm.Show($@"{Ai.中括号左}{betLottery}@{IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue}{Ai.中括号右}飞单成功.", AlertType.Success);
                    // });

                    // 播放成功提示音,然后继续下一轮循环
                    SoundHelper.MediaBetSuccess.Play();
                }
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "ToBetError", ex.ToString());
        }
    }

    /// <summary>
    /// 检查结果
    /// </summary>
    public static async Task CheckBetResult()
    {
        // 循环10次,每次间隔2秒
        for (int i = 1; i <= 10; i++)
        {
            await Task.Delay(2000);
            try
            {
                // 取出所有未知结果
                List<BetResult> betResultList = await DbHelper.FSql.Select<BetResult>()
                    .Where(a => a.Result == EnumBetResult.未知)
                    .ToListAsync();

                // 没有需要检查的结果
                if (!betResultList.Any())
                {
                    return;
                }

                await DbHelper.AddLog(EnumLogType.机器人, "检查飞单结果", $"{Ai.中括号左}{betResultList[0].BetLottery}{Ai.中括号右}{Ai.中括号左}{betResultList[0].Issue}{Ai.中括号右}期开始第{i}轮飞单检查.");

                // 发送请求
                string url = $"{Setting.Current.PlatformHost}/BetResult/";
                try
                {
                    string result = await url
                        .PostAsync()
                        .ReceiveString();
                    if (string.IsNullOrWhiteSpace(result))
                    {
                        continue;
                    }

                    JArray jsonObj = JArray.Parse(result);
                    foreach (JToken jToken in jsonObj)
                    {
                        // 取出游戏名称和期号
                        string gameName = jToken["GameName"]!.ToString();
                        gameName = gameName.Replace("168", "一六八");
                        string issue = jToken["Issue"]!.ToString();

                        // 取出BetResult
                        BetResult betResult = await DbHelper.FSql.Select<BetResult>()
                            .Where(a => a.BetLottery.ToString() == gameName)
                            .Where(a => a.Issue == issue)
                            .Where(a => a.Result == EnumBetResult.未知)
                            .FirstAsync();

                        // 判断结果
                        if (betResult == null)
                        {
                            continue;
                        }

                        // 更新汇总表
                        await DbHelper.FSql.Update<HuiZong>()
                            .Set(a => a.BetResult, EnumBetResult.成功)
                            .Where(a => a.BetLottery == betResult.BetLottery)
                            .Where(a => a.Issue == betResult.Issue)
                            .ExecuteAffrowsAsync();

                        // 更新游戏种类结果
                        await DbHelper.FSql.Update<BetResult>()
                            .Set(a => a.Result, EnumBetResult.成功)
                            .Where(a => a.BetLottery == betResult.BetLottery)
                            .Where(a => a.Issue == betResult.Issue)
                            .ExecuteAffrowsAsync();

                        // 记录日志
                        await DbHelper.AddLog(EnumLogType.平台, "飞单成功CR", $@"{Ai.中括号左}{betResult.BetLottery}@{betResult.Issue}{Ai.中括号右}飞单成功.");

                        // 弹出提示框
                        // 在非 UI 线程中调用
                        CommonHelper.Context.Post(_ =>
                        {
                            FormAlertMessage frm = new FormAlertMessage();
                            frm.Show($@"{Ai.中括号左}{betResult.BetLottery}@{betResult.Issue}{Ai.中括号右}飞单成功.", AlertType.Success);
                        }, null);

                        // Invoke(() =>
                        // {
                        //     FormAlertMessage frm = new FormAlertMessage();
                        //     frm.Show($@"{Ai.中括号左}{betResult.BetLottery}@{betResult.Issue}{Ai.中括号右}飞单成功.", AlertType.Success);
                        // });

                        // 播放成功提示音,然后继续下一轮循环
                        SoundHelper.MediaBetSuccess.Play();
                    }
                }
                catch (Exception ex)
                {
                    await DbHelper.AddLog(EnumLogType.机器人, "CheckBetResult", ex.ToString());
                }
            }
            catch (Exception ex)
            {
                await DbHelper.AddLog(EnumLogType.机器人, "CheckBetResult", ex.ToString());
            }
        }
    }

    /// <summary>
    /// 显示失败结果
    /// </summary>
    public static async Task ShowBetResultFailed()
    {
        try
        {
            // 取出所有未知结果
            List<BetResult> betResultList = await DbHelper.FSql.Select<BetResult>()
                .Where(a => a.Result == EnumBetResult.未知)
                .ToListAsync();

            // 没有需要检查的结果
            if (!betResultList.Any())
            {
                return;
            }

            // 遍历结果
            foreach (BetResult betResult in betResultList)
            {
                // 更新汇总表
                await DbHelper.FSql.Update<HuiZong>()
                    .Set(a => a.BetResult, EnumBetResult.失败)
                    .Where(a => a.BetLottery == betResult.BetLottery)
                    .Where(a => a.Issue == IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue)
                    .ExecuteAffrowsAsync();

                // 更新游戏种类结果
                await DbHelper.FSql.Update<BetResult>()
                    .Set(a => a.Result, EnumBetResult.失败)
                    .Where(a => a.BetLottery == betResult.BetLottery)
                    .Where(a => a.Issue == betResult.Issue)
                    .ExecuteAffrowsAsync();

                // 记录日志
                await DbHelper.AddLog(EnumLogType.平台, "飞单失败", $@"{Ai.中括号左}{betResult.BetLottery}@{betResult.Issue}{Ai.中括号右}飞单失败.");

                // 弹出提示框
                CommonHelper.Context.Post(_ =>
                {
                    FormAlertMessage frm = new FormAlertMessage();
                    frm.Show($@"{Ai.中括号左}{betResult.BetLottery}@{betResult.Issue}{Ai.中括号右}飞单失败.", AlertType.Error);
                }, null);

                // Invoke(() =>
                // {
                //     FormAlertMessage frm = new FormAlertMessage();
                //     frm.Show($@"{Ai.中括号左}{betResult.BetLottery}@{betResult.Issue}{Ai.中括号右}飞单失败.", AlertType.Error);
                // });

                // 播放失败提示音
                SoundHelper.MediaBetFalse.Play();
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "ShowBetResultFailed", ex.ToString());
        }
    }

    /// <summary>
    /// 自动撤销失败的订单
    /// </summary>
    public static async Task AutoCancelBetFailedData()
    {
        try
        {
            // 取出飞单失败的彩种
            List<BetResult> betResultList = await DbHelper.FSql.Select<BetResult>()
                .Where(a => a.Issue == Setting.Current.LastBetIssue)
                .Where(a => a.Result == EnumBetResult.失败)
                .ToListAsync();

            // 遍历失败的彩种
            foreach (BetResult betResult in betResultList)
            {
                // 从BetOrder表中取出所有失败的订单
                List<BetOrder> betOrderList = await DbHelper.FSql.Select<BetOrder>()
                    .Where(a => a.BetLottery == betResult.BetLottery) // 种类
                    .Where(a => a.Issue == betResult.Issue) // 期号
                    .Where(a => a.BetOrderStatus == EnumBetOrderStatus.已受理) // 状态
                    .ToListAsync();
                CancelBetData(betOrderList);
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "CancelBetFailedData", ex.ToString());
        }
    }

    /// <summary>
    /// 撤销指定数据
    /// </summary>
    public static async void CancelBetData(List<BetOrder> toBetOrderList)
    {
        try
        {
            // 取出指定期号的所有投注用户
            List<string> accountList = toBetOrderList.Select(a => a.Account).Distinct().ToList();

            // 遍历用户,撤销指定期号投注数据
            foreach (string account in accountList)
            {
                Member member = await DbHelper.GetMember(account);
                await RobotHelper.CancelBetDataHandler(member, toBetOrderList.First().Issue, new ReceiveMessage(), toBetOrderList);
            }

            // 发送撤销成功消息
            // string gameName = comboBox_BetLottery.Text.Trim();
            string gameName = "";
            await ChatHelper.SendGroupMessage($"所有贵宾请注意,{Ai.中括号左}{toBetOrderList.First().Issue}{Ai.中括号右}{Ai.中括号左}{gameName}{Ai.中括号右}答题数据已被撤销,积分已返还各自账户,请注意查收.");

            // 发送当前会员信息
            RobotHelper.SendMemberInfo();

            // 记录日志,显示成功提示
            await DbHelper.AddLog(EnumLogType.机器人, "CancelBetDataSuccess", toBetOrderList.First().Issue);
            await Task.Run(() => { MessageBox.Show($@"撤销{Ai.中括号左}{toBetOrderList.First().Issue}{Ai.中括号右}{Ai.中括号左}{gameName}{Ai.中括号右}答题数据成功.", @"操作提醒", MessageBoxButtons.OK, MessageBoxIcon.Information); });
        }
        catch (Exception ex)
        {
            // 记录日志,显示失败提示
            await DbHelper.AddLog(EnumLogType.机器人, "CancelBetDataError", "issue:" + ex);
            await Task.Run(() => { MessageBox.Show(@"操作提醒", $@"撤销{Ai.中括号左}{toBetOrderList.First().Issue}{Ai.中括号右}期数据失败,请检查日志."); });
        }
    }
}