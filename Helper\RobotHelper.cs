﻿using System.Collections.Concurrent;
using System.Text;
using System.Text.RegularExpressions;
using AiHelper;
using FreeSql;
using Robot.ChatPlatform;
using Robot.Config;
using Robot.Enum;
using Robot.Models;

namespace Robot.Helper;

/// <summary>
/// Robot助手类,用于处理一些常用功能,如获取Robot信息,获取群列表,发送消息等
/// </summary>
public static class RobotHelper
{
    #region 全局变量

    public static RobotInfo RobotInfo { get; set; } = new();
    public static string WorkGroupId { get; set; } = string.Empty;

    public static ConcurrentDictionary<string, string> GroupDic { get; set; } = new();

    // public static BlockingCollection<ReceiveMessage> ReceiveMessageQueue { get; set; } = new();
    // public static long OpenDataCount { get; set; }
    public static List<string> OpenTipsList { get; } = new();
    public static List<string> CloseDownTipsList { get; } = new();
    public static List<string> CloseTipsList { get; } = new();

    #endregion

    #region 处理监听消息Handler

    /// <summary>
    /// 处理监听消息Handler
    /// </summary>
    /// <param name="msg"></param>
    public static async Task ReceiveMessageHandlerAsync(ReceiveMessage msg)
    {
        // 从数据库查出此account的member信息
        Member? member = await DbHelper.FSql.Select<Member>()
            .Where(a => a.Account.Equals(msg.Account))
            .ToOneAsync();

        try
        {
            // 该用户不存在则先创建用户
            if (member == null)
            {
                if (msg.Account.Trim() == "")
                {
                    string returnMsg = $"{Ai.中括号左}{msg.Content}{Ai.中括号右}\r因通讯App原因,该消息未能识别答题用户,请该用户重试.";
                    await ChatHelper.SendGroupMessage(returnMsg);
                    return;
                }

                // 创建用户
                member = new Member
                {
                    Account = msg.Account,
                    回水比例 = Setting.Current.回水比例
                };

                // 写入数据库
                await DbHelper.FSql.Insert<Member>()
                    .AppendData(member)
                    .ExecuteIdentityAsync();
            }

            // 判断更新昵称
            if (string.IsNullOrWhiteSpace(member.昵称))
            {
                string nick = await ChatHelper.GetNick(member.Account);
                member.昵称 = nick;
                member.备注名 = member.昵称;

                // 更新数据库
                await DbHelper.FSql.Update<Member>()
                    .Set(a => a.昵称, nick)
                    .Set(a => a.备注名, nick)
                    .Where(a => a.Account.Equals(msg.Account))
                    .ExecuteAffrowsAsync();
            }

            // 处理换行符
            string msgCon = msg.Content
                .Replace(Environment.NewLine, " ")
                .Replace("\\r\\n", " ")
                .Replace("\\r", " ")
                .Replace("\\n", " ")
                .Replace("\r\n", " ")
                .Replace("\r", " ")
                .Replace("\n", " ")
                .Replace("  ", " ")
                .Trim();

            // 判断消息内容否为空
            if (string.IsNullOrWhiteSpace(msgCon))
            {
                string returnMsg = $"{Ai.五角星}答题错误,请检查格式{CommonHelper.GetFaceIdChaCha()}";
                await ChatHelper.SendGroupMessage(returnMsg, member.Account);
                return;
            }

            // 判断指令类型
            if (msgCon == "0")
            {
                await 指令说明Handler(member);
            }
            else if (msgCon == "1" || msgCon == "查" || msgCon == "查分")
            {
                await 查分指令Handler(member);
            }
            else if (msgCon == "2" || msgCon == "图" || msgCon == "路")
            {
                await 发送路子图Handler();
            }
            else if (msgCon == "3")
            {
                await 发送开奖图Handler();
            }
            else if (msgCon == "4" || msgCon == "回" || msgCon == "回水" || msgCon == "返水" || msgCon == "反水")
            {
                await RebateHandler(member, msg);
            }
            else if (msgCon == "5" || msgCon == "水" || msgCon == "流水")
            {
                await 流水详情Handler(member);
            }
            else if (msgCon == "6" || msgCon == "时间" || msgCon == "封" || msgCon == "开")
            {
                await 查询时间指令Handler(member);
            }
            else if (msgCon == "7" || msgCon == "撤" || msgCon == "撤销" || msgCon == "取消")
            {
                await CancelBetDataHandler(member, IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue, msg, new List<BetOrder>());
            }
            else if (msgCon == "8")
            {
                await SendTanImageFullHandler();
            }
            else if (msgCon == "9")
            {
                await SendSettleReportHandler();
            }
            else if ((Regex.Matches(msgCon, "上[1-9]\\d*$").Count > 0 && Regex.Matches(msgCon, "上[1-9]\\d*$")[0].Value.Length.Equals(msgCon.Length)) ||
                     (Regex.Matches(msgCon, "上分[1-9]\\d*$").Count > 0 && Regex.Matches(msgCon, "上分[1-9]\\d*$")[0].Value.Length.Equals(msgCon.Length)) ||
                     (Regex.Matches(msgCon, "起[1-9]\\d*$").Count > 0 && Regex.Matches(msgCon, "起[1-9]\\d*$")[0].Value.Length.Equals(msgCon.Length)) ||
                     (Regex.Matches(msgCon, "起分[1-9]\\d*$").Count > 0 && Regex.Matches(msgCon, "起分[1-9]\\d*$")[0].Value.Length.Equals(msgCon.Length)))
            {
                await 上分指令Handler(member, msg);
            }
            else if ((Regex.Matches(msgCon, "下[0-9]+(\\.[0-9]+)?$").Count > 0 && Regex.Matches(msgCon, "下[0-9]+(\\.[0-9]+)?$")[0].Value.Length.Equals(msgCon.Length)) ||
                     (Regex.Matches(msgCon, "下分[0-9]+(\\.[0-9]+)?$").Count > 0 && Regex.Matches(msgCon, "下分[0-9]+(\\.[0-9]+)?$")[0].Value.Length.Equals(msgCon.Length)) ||
                     (Regex.Matches(msgCon, "落[0-9]+(\\.[0-9]+)?$").Count > 0 && Regex.Matches(msgCon, "落[0-9]+(\\.[0-9]+)?$")[0].Value.Length.Equals(msgCon.Length)) ||
                     (Regex.Matches(msgCon, "落分[0-9]+(\\.[0-9]+)?$").Count > 0 && Regex.Matches(msgCon, "落分[0-9]+(\\.[0-9]+)?$")[0].Value.Length.Equals(msgCon.Length)))
            {
                await 下分指令Handler(member, msg);
            }
            else
            {
                await 下注指令Handler(member, msg);
            }
        }
        catch (Exception ex)
        {
            string returnMsg = $"{Ai.五角星}答题错误,请检查格式{CommonHelper.GetFaceIdChaCha()}";
            await ChatHelper.SendGroupMessage(returnMsg, member.Account);
            await DbHelper.AddLog(EnumLogType.机器人, Ai.中括号左 + "ReceiveMessageHandlerAsyncError" + Ai.中括号右, ex.ToString());
        }
    }

    #endregion

    #region 0.查询指令说明

    /// <summary>
    /// 处理查询指令
    /// </summary>
    private static async Task 指令说明Handler(Member member)
    {
        try
        {
            string returnMsg = "\r";
            returnMsg += "0:指令说明\r";
            returnMsg += "1:查询积分及当前答题详情\r";
            returnMsg += "2:路子图\r";
            returnMsg += "3:答案图\r";
            returnMsg += "4:回水\r";
            returnMsg += "5:流水详情\r";
            returnMsg += "6:查询时间\r";
            returnMsg += "7:撤销答题\r";
            returnMsg += "8:查询最后封卷详情\r";
            returnMsg += "9:查询最后结算详情\r";
            returnMsg += "其它指令请咨询管理员\r";

            // Debug.WriteLine($@"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}]开始发送返回消息");
            await ChatHelper.SendGroupMessage(returnMsg, member.Account);
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, Ai.中括号左 + "指令说明HandlerError" + Ai.中括号右, ex.ToString());
        }
    }

    #endregion

    #region 1.处理查询指令

    /// <summary>
    /// 处理查询指令
    /// </summary>
    /// <param name="member"></param>
    private static async Task 查分指令Handler(Member member)
    {
        try
        {
            MemberInfo memberInfo = GetMemberDetail(member).Result;
            string betDetail = string.Empty;

            // 获取当前期投注明细
            List<BetOrder> betOrderList = await GetBetOrderList(member.Account, IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue);

            // 取出所有投注类型
            HashSet<EnumBetLottery> betLotteryList = [..betOrderList.Select(betOrder => betOrder.BetLottery)];
            betLotteryList = [..betLotteryList.OrderBy(enumBetLottery => (int)enumBetLottery)];

            // 遍历所有投注类型
            foreach (EnumBetLottery betLottery in betLotteryList)
            {
                betDetail += $"{Ai.中括号左}{betLottery}{Ai.中括号右}:\r";
                Dictionary<string, decimal> betContentMoneyDic = CommonHelper.PlayContentList.ToDictionary(playType => playType, _ => (decimal)0);
                foreach (var betOrder in betOrderList.Where(order => order.BetLottery == betLottery))
                {
                    betContentMoneyDic[betOrder.Con] += betOrder.Money;
                }

                // 拼接投注明细
                foreach (var kv in betContentMoneyDic.Where(kv => kv.Value > 0))
                {
                    betDetail += $"{kv.Key}/{kv.Value}\r";
                }
            }

            // 拼接返回消息
            StringBuilder returnMsgBuilder = new StringBuilder();
            returnMsgBuilder.Append($"现积分{CommonHelper.GetFaceIdMoneyBag()}: {Math.Round(member.Balance, 2)}\r");
            returnMsgBuilder.Append($"可回水{CommonHelper.GetFaceIdWater()}: {Math.Round(memberInfo.未回流水 / 100 * member.回水比例, 2)}\r");
            returnMsgBuilder.Append($"现结算{CommonHelper.GetFaceIdMoneyBag()}: {Math.Round(memberInfo.总盈亏, 2)}\r");
            if (!string.IsNullOrEmpty(betDetail))
            {
                returnMsgBuilder.Append("本期答题:\r");
                returnMsgBuilder.Append(betDetail);
            }
            else
            {
                returnMsgBuilder.Append($"{Ai.中括号左}{IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue}{Ai.中括号右}查无答题记录");
            }

            string returnMsg = returnMsgBuilder.ToString();

            // Debug.WriteLine($@"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}]开始发送返回消息");
            await ChatHelper.SendGroupMessage(returnMsg, member.Account);
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, Ai.中括号左 + "查分指令HandlerError" + Ai.中括号右, ex.ToString());
        }
    }

    #endregion

    #region 2.发送路子图

    /// <summary>
    /// 发送路子图
    /// </summary>
    public static async Task 发送路子图Handler()
    {
        if (Setting.Current.SendImageRows7)
        {
            await ChatHelper.SendImage(ImageHelper.TanRows7ImagePath);
        }

        if (Setting.Current.SendImageRows6)
        {
            await Task.Delay(500);
            await ChatHelper.SendImage(ImageHelper.TanRows6ImagePath);
        }
    }

    /// <summary>
    /// 发送路子图
    /// </summary>
    private static async Task 发送路子图FullHandler()
    {
        await Task.Delay(0);
        if (Setting.Current.SendImageRows7)
        {
            await ChatHelper.SendImage(ImageHelper.TanRows77ImagePath);
        }

        if (Setting.Current.SendImageRows6)
        {
            await Task.Delay(500);
            await ChatHelper.SendImage(ImageHelper.TanRows66ImagePath);
        }
    }

    #endregion

    #region 3.发送开奖图

    /// <summary>
    /// 发送开奖图
    /// </summary>
    public static async Task 发送开奖图Handler()
    {
        await ChatHelper.SendImage(ImageHelper.DrawImagePath);
    }

    #endregion

    #region 4.处理回水

    /// <summary>
    /// 处理回水
    /// </summary>
    private static async Task RebateHandler(Member member, ReceiveMessage msg)
    {
        try
        {
            // 判断回水比例
            if (member.回水比例 <= 0)
            {
                // 信息内容不为空则是用户主动发起的申请,需要回复给用户
                if (!string.IsNullOrEmpty(msg.Content))
                {
                    string tmpReturnMsg = @"你的回水比例为0,请与管理员联系";
                    await ChatHelper.SendGroupMessage(tmpReturnMsg, member.Account);
                }

                return;
            }

            // 先从数据库取出此用户的信息
            decimal oldBalance = member.Balance;

            // 取出该用户当前所有可回水订单
            List<BetOrder>? betOrderList = await DbHelper.FSql.Select<BetOrder>()
                .Where(a => a.Account.Equals(member.Account))
                .Where(a => a.WinLose == EnumBetWinLose.中 || a.WinLose == EnumBetWinLose.挂)
                .Where(a => a.BetOrderRebateStatus.Equals(EnumBetOrderRebateStatus.未回水))
                .ToListAsync();

            // 判断是否有可回水订单
            if (!betOrderList.Any())
            {
                if (!string.IsNullOrEmpty(msg.Content))
                {
                    string tmpReturnMsg = "\r" + $"{Ai.五角星}你当前没有可回水数据";
                    await ChatHelper.SendGroupMessage(tmpReturnMsg, member.Account);
                }

                return;
            }

            // 逐一计算回水金额
            foreach (BetOrder order in betOrderList)
            {
                order.回水金额 = Math.Round(order.Money / 100 * member.回水比例, 2);
            }

            // 计算回水总金额
            decimal returnCommissionSum = betOrderList.Sum(a => a.回水金额);
            member.Balance += returnCommissionSum;
            CommonHelper.OneKeyRebateResult += $"{CommonHelper.GetFaceIdStar()}{member.昵称}{CommonHelper.GetFaceIdWater()}:{Math.Round(returnCommissionSum, 2)}\r";

            // 使用事务操作数据库
            using DbContext? dbContext = DbHelper.FSql.CreateDbContext();

            // 更新会员信息
            await DbHelper.FSql.Update<Member>()
                .Set(a => a.Balance, member.Balance)
                .Where(a => a.Account.Equals(member.Account))
                .ExecuteAffrowsAsync();

            // 添加财务变更记录
            Finance finance = new Finance
            {
                Account = member.Account,
                变动前 = oldBalance,
                变动值 = returnCommissionSum,
                变动后 = member.Balance,
                凭据 = @"申请回水",
                对应信息 = $"{msg.Id}"
            };

            finance.Id = await DbHelper.FSql.Insert<Finance>()
                .AppendData(finance)
                .ExecuteIdentityAsync();

            // 更新订单回水状态和回水金额
            foreach (BetOrder order in betOrderList)
            {
                await DbHelper.FSql.Update<BetOrder>()
                    .Set(a => a.BetOrderRebateStatus, EnumBetOrderRebateStatus.已回水)
                    .Set(a => a.回水比例, member.回水比例)
                    .Set(a => a.回水金额, order.回水金额)
                    .Where(a => a.Id.Equals(order.Id))
                    .ExecuteAffrowsAsync();
            }

            // 保存到数据库
            await dbContext.SaveChangesAsync();

            // 根据msg内容判断是否发送消息
            if (!string.IsNullOrEmpty(msg.Content))
            {
                string tmpReturnMsg = "\r" + $"{CommonHelper.GetFaceIdGreenDuiGou()}回水成功\r";
                if (Setting.Current.显示回水金额.Equals(1))
                {
                    tmpReturnMsg += $"本次回水{CommonHelper.GetFaceIdWater()}:{Math.Round(returnCommissionSum, 2)}\r";
                }

                tmpReturnMsg += $"当前可用{CommonHelper.GetFaceIdMoneyBag()}:{Math.Round(member.Balance, 2)}";
                await ChatHelper.SendGroupMessage(tmpReturnMsg, member.Account);
            }

            // 添加日志
            await DbHelper.AddLog(EnumLogType.机器人, "处理回水", $"回水账号{Ai.中括号左}{member.Account}{Ai.中括号右}");
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 计算回水 错误:" + ex);
        }
    }

    #endregion

    #region 5.流水详情

    /// <summary>
    /// 流水详情
    /// </summary>
    /// <param name="member"></param>
    private static async Task 流水详情Handler(Member member)
    {
        try
        {
            MemberInfo memberInfo = GetMemberDetail(member).Result;

            // 返回消息
            StringBuilder tmpReturnMsg = new StringBuilder();
            tmpReturnMsg.Append("今日详情:\r");
            tmpReturnMsg.Append($@"总流水{CommonHelper.GetFaceIdMoneyBag()}:{memberInfo.总流水}" + "\r");
            tmpReturnMsg.Append($@"今日{CommonHelper.GetFaceIdWater()}:{Math.Round(memberInfo.总流水 / 100 * member.回水比例, 2)}" + "\r");
            tmpReturnMsg.Append($@"已回{CommonHelper.GetFaceIdWater()}:{Math.Round(memberInfo.已回金额, 2)}" + "\r");
            tmpReturnMsg.Append($@"未回{CommonHelper.GetFaceIdWater()}:{Math.Round(memberInfo.未回流水 / 100 * member.回水比例, 2)}" + "\r");
            tmpReturnMsg.Append($@"今日上{CommonHelper.GetFaceIdMoneyBag()}:{memberInfo.总上分}" + "\r");
            tmpReturnMsg.Append($@"今日下{CommonHelper.GetFaceIdMoneyBag()}:{memberInfo.总下分}" + "\r");
            tmpReturnMsg.Append($@"今日进{CommonHelper.GetFaceIdMoneyBag()}:{Math.Round(memberInfo.总盈亏, 2)}" + "\r");
            tmpReturnMsg.Append($@"现积分{CommonHelper.GetFaceIdMoneyBag()}:{Math.Round(memberInfo.积分, 2)}" + "\r");
            string returnMsg = tmpReturnMsg.ToString();
            await ChatHelper.SendGroupMessage(returnMsg, member.Account);
        }
        catch (Exception ex)
        {
            await ChatHelper.SendGroupMessage("查询流水详情出错,请联系管理员", member.Account);
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 流水详情 错误:" + ex);
        }
    }

    #endregion

    #region 6.发送时间

    /// <summary>
    /// 发送时间
    /// </summary>
    /// <param name="member"></param>
    public static async Task 查询时间指令Handler(Member? member)
    {
        try
        {
            int tmpCloseCountDown = IssueTimeHelper.CloseTimeDownDic[CommonHelper.Lottery] - Setting.Current.封盘时间;
            int tmpOpenCountDown = IssueTimeHelper.OpenTimeDownDic[CommonHelper.Lottery];

            string tmpCloseTimeTips;
            if (tmpCloseCountDown > 0)
            {
                string tmpHourStr;
                string tmpMinuteStr;
                string tmpSecondStr;

                int tmpHour = tmpCloseCountDown / 3600;
                int tmpMinute = tmpCloseCountDown / 60;
                int tmpSecond = tmpCloseCountDown % 60;
                if (tmpHour == 0)
                {
                    tmpHourStr = "00";
                }
                else
                {
                    tmpHourStr = tmpHour.ToString();
                    tmpHourStr = tmpHourStr.Length == 1 ? "0" + tmpHourStr : tmpHourStr;
                }

                if (tmpMinute == 0)
                {
                    tmpMinuteStr = "00";
                }
                else
                {
                    tmpMinuteStr = tmpMinute.ToString();
                    tmpMinuteStr = tmpMinuteStr.Length == 1 ? "0" + tmpMinuteStr : tmpMinuteStr;
                }

                if (tmpSecond == 0)
                {
                    tmpSecondStr = "00";
                }
                else
                {
                    tmpSecondStr = tmpSecond.ToString();
                    tmpSecondStr = tmpSecondStr.Length == 1 ? "0" + tmpSecondStr : tmpSecondStr;
                }

                tmpCloseTimeTips = string.Concat(tmpHourStr, ":", tmpMinuteStr, ":", tmpSecondStr);
            }
            else
            {
                tmpCloseTimeTips = "已封盘";
            }

            string tmpOpenTimeTips;
            if (tmpOpenCountDown > 0)
            {
                string tmpHourStr;
                string tmpMinuteStr;
                string tmpSecondStr;

                int tmpHour = tmpOpenCountDown / 3600;
                int tmpMinute = tmpOpenCountDown / 60;
                int tmpSecond = tmpOpenCountDown % 60;
                if (tmpHour == 0)
                {
                    tmpHourStr = "00";
                }
                else
                {
                    tmpHourStr = tmpHour.ToString();
                    tmpHourStr = tmpHourStr.Length == 1 ? "0" + tmpHourStr : tmpHourStr;
                }

                if (tmpMinute == 0)
                {
                    tmpMinuteStr = "00";
                }
                else
                {
                    tmpMinuteStr = tmpMinute.ToString();
                    tmpMinuteStr = tmpMinuteStr.Length == 1 ? "0" + tmpMinuteStr : tmpMinuteStr;
                }

                if (tmpSecond == 0)
                {
                    tmpSecondStr = "00";
                }
                else
                {
                    tmpSecondStr = tmpSecond.ToString();
                    tmpSecondStr = tmpSecondStr.Length == 1 ? "0" + tmpSecondStr : tmpSecondStr;
                }

                tmpOpenTimeTips = string.Concat(tmpHourStr, ":", tmpMinuteStr, ":", tmpSecondStr);
            }
            else
            {
                tmpOpenTimeTips = "已封盘";
            }

            // 构造返回消息
            StringBuilder tmpReturnMsgBuilder = new StringBuilder();
            tmpReturnMsgBuilder.Append($@"当前题号:{Ai.中括号左}{IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue}{Ai.中括号右}" + "\r");
            tmpReturnMsgBuilder.Append($@"距离封卷:{Ai.中括号左}{tmpCloseTimeTips}{Ai.中括号右}" + "\r");
            tmpReturnMsgBuilder.Append($@"预计开卷:{Ai.中括号左}{tmpOpenTimeTips}{Ai.中括号右}");
            string returnMsg = tmpReturnMsgBuilder.ToString();
            if (member == null)
            {
                await ChatHelper.SendGroupMessage(returnMsg);
            }
            else
            {
                await ChatHelper.SendGroupMessage(returnMsg, member.Account);
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 查询时间 错误:" + ex);
        }
    }

    #endregion

    #region 7.取消订单

    /// <summary>
    /// 取消订单
    /// </summary>
    /// <param name="member"></param>
    /// <param name="issue"></param>
    /// <param name="msg"></param>
    /// <param name="toBetOrderList"></param>
    public static async Task CancelBetDataHandler(Member member, string issue, ReceiveMessage msg, List<BetOrder> toBetOrderList)
    {
        string returnMsg = string.Empty;

        try
        {
            // 如果是用户发起的撤单请求,则需要判断是否允许撤单 (如果消息内容为空,则是管理员发起的撤单请求)
            if (!string.IsNullOrWhiteSpace(msg.Content) && !Setting.Current.是否允许撤单)
            {
                returnMsg = "对不起,当前系统不允许撤销答题.";
                return;
            }

            // 如果是用户发起的撤单请求,则需要判断是否已经封盘
            if (!string.IsNullOrWhiteSpace(msg.Content) && !string.IsNullOrWhiteSpace(issue) && CloseTipsList.Contains(issue))
            {
                returnMsg = $"已封卷{CommonHelper.GetFaceIdChaCha()} {Ai.中括号左}撤销{Ai.中括号右}无效.";
                return;
            }

            // 获取当前期投注明细
            List<BetOrder> betOrderList;
            if (!string.IsNullOrWhiteSpace(msg.Content) && !string.IsNullOrWhiteSpace(issue))
            {
                betOrderList = await GetBetOrderList(member.Account, issue);
            }
            else
            {
                betOrderList = toBetOrderList.Where(a => a.Account == member.Account).ToList();
            }

            //没有答题数据
            if (!betOrderList.Any())
            {
                returnMsg = "\r" + $"{Ai.五角星}你当前没有可撤销数据";
                return;
            }

            // 使用事务操作数据库
            using DbContext? dbContext = DbHelper.FSql.CreateDbContext();

            //遍历答题明细
            foreach (BetOrder betOrder in betOrderList)
            {
                //更新答题表
                await DbHelper.FSql.Update<BetOrder>()
                    .Set(a => a.BetOrderStatus, string.IsNullOrWhiteSpace(msg.Content) ? EnumBetOrderStatus.管理撤销 : EnumBetOrderStatus.用户撤销)
                    .Where(a => a.Id == betOrder.Id)
                    .ExecuteAffrowsAsync();

                // 更新会员信息
                decimal oldBalance = member.Balance;
                member.Balance += betOrder.Money; // 退回金额
                await DbHelper.FSql.Update<Member>()
                    .Set(a => a.Balance, member.Balance)
                    .Where(a => a.Account == member.Account)
                    .ExecuteAffrowsAsync();

                //记录财务凭证
                Finance fn = new Finance
                {
                    Account = member.Account,
                    变动前 = oldBalance,
                    变动值 = betOrder.Money,
                    变动后 = member.Balance,
                    凭据 = "撤销答题退回",
                    对应信息 = $"BetOrderId{Ai.中括号左}{betOrder.Id}{Ai.中括号右}"
                };
                await DbHelper.FSql.Insert<Finance>()
                    .AppendData(fn)
                    .ExecuteIdentityAsync();
            }

            // 保存到数据库
            await dbContext.SaveChangesAsync();

            // 构造返回消息
            returnMsg = $"{Ai.五角星}订单已撤销{CommonHelper.GetFaceIdMoneyBag()}:{Math.Round(member.Balance, 2)}";
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "错误信息摘要", ex.ToString());
        }
        finally
        {
            if (!string.IsNullOrWhiteSpace(msg.Content))
            {
                await ChatHelper.SendGroupMessage(returnMsg, member.Account);
            }
        }
    }

    #endregion

    #region 8.发送全部数据摊图

    private static async Task SendTanImageFullHandler()
    {
        try
        {
            // await ChatHelper.SendGroupMessage("(全图)指令开发中。");
            await 发送路子图FullHandler();
            // List<CloseTimeBetReport>? report = await DbHelper.FSql.Select<CloseTimeBetReport>()
            //     .OrderByDescending(a => a.Id)
            //     .Take(1)
            //     .ToListAsync();
            //
            // if (report != null && report.Any())
            // {
            //     SendGroupMessage(report.First().Report + "\r" + $"以上是{Ai.中括号左}{report.First().Issue}{Ai.中括号右}的封卷答题报告");
            // }
            // else
            // {
            //     SendGroupMessage("当前暂时没有封卷报告答题记录");
            // }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 SendCloseTimeBetReportHandler 错误:" + ex);
        }
    }

    #endregion

    #region 9.发送结算报告记录

    /// <summary>
    /// 发送结算报告记录
    /// </summary>
    private static async Task SendSettleReportHandler()
    {
        try
        {
            await ChatHelper.SendGroupMessage("指令[9]已被暂时停用。");

            // List<SettleReport>? report = await DbHelper.FSql.Select<SettleReport>()
            //     .OrderByDescending(a => a.Id)
            //     .Take(1)
            //     .ToListAsync();
            //
            // if (report != null && report.Any())
            // {
            //     SendGroupMessage(report.First().Report + "\r" + $"以上为{Ai.中括号左}{report.First().Issue}{Ai.中括号右}的结算报告副本");
            // }
            // else
            // {
            //     SendGroupMessage("当前暂时没有结算报告记录");
            // }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "捕获 SendSettleReportHandler 错误:" + ex);
        }
    }

    #endregion

    #region 处理上分指令

    private static async Task 上分指令Handler(Member member, ReceiveMessage msg)
    {
        string returnMsg = string.Empty;

        try
        {
            // 查询该用户是否还有未处理的上分申请
            AddMoney? addMoney = await DbHelper.FSql.Select<AddMoney>()
                .Where(a => a.Account == member.Account)
                .Where(a => a.Status == EnumBalanceStatus.等待处理)
                .ToOneAsync();

            // 如果有未处理的上分申请
            if (addMoney != null)
            {
                SoundHelper.MediaRechargeWithdrawal.Play();
                await DbHelper.AddLog(EnumLogType.机器人, "驳回上分申请", string.Concat(Ai.中括号左, member.Account, Ai.小圆点, member.昵称, Ai.中括号右, "该客户上条上分消息尚未处理,请及时处理"));

                // 构造返回消息
                returnMsg = "你有一条" + Ai.中括号左 + "上" + addMoney.Money + Ai.中括号右 + "的申请还未处理,请等待处理完成" + "\r";
                return;
            }

            // 从消息中提取金额
            decimal 分数 = Convert.ToDecimal(Regex.Matches(msg.Content, "[1-9]\\d*$")[0].Value);

            // 创建AddMoney订单
            addMoney = new AddMoney
            {
                Account = member.Account,
                Money = 分数,
                Status = EnumBalanceStatus.等待处理,
                FromMsgId = msg.Id
            };

            // 记录进数据库
            await DbHelper.FSql.Insert<AddMoney>()
                .AppendData(addMoney)
                .ExecuteIdentityAsync();

            await DbHelper.AddLog(EnumLogType.机器人, "申请上分", $"[{msg.Id}]{Ai.中括号左}{member.Account}{Ai.小圆点}{member.昵称}{Ai.中括号右}申请上分{Ai.中括号左}{分数}{Ai.中括号右}请及时审核.");
            returnMsg = $"已收到你{Ai.中括号左}{msg.Content}{Ai.中括号右}的申请,请等待审核.";

            // 显示到表格
            // Common.ShowAddMoneyOrder(member.Account, member.备注名, addMoney.Money);

            // 播放声音提醒
            SoundHelper.MediaRechargeWithdrawal.Play();
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "上分指令Handler", ex.ToString());
        }
        finally
        {
            await ChatHelper.SendGroupMessage(returnMsg, member.Account);
        }
    }

    #endregion

    #region 处理下分指令

    private static async Task 下分指令Handler(Member member, ReceiveMessage msg)
    {
        string returnMsg = string.Empty;
        try
        {
            // 申请下分
            decimal backMoney = Convert.ToDecimal(Regex.Matches(msg.Content, "[0-9]+(\\.[0-9]+)?$")[0].Value);

            // 下分金额不能小于1
            if (backMoney < 1)
            {
                // 构造返回消息
                returnMsg += $"申请【下{backMoney}】失败,分数不足1" + "\r";
                returnMsg += $"{CommonHelper.GetFaceIdMoneyBag()}:{Math.Round(member.Balance, 2)}" + "\r";
                return;
            }

            // 判断可用分数是否足够申请下分
            if (member.Balance < backMoney)
            {
                await DbHelper.AddLog(EnumLogType.机器人, "驳回下分申请",
                    string.Concat("[", msg.Id, "]", Ai.中括号左, member.Account, Ai.小圆点, member.昵称, Ai.中括号右,
                        "申请下分", Ai.中括号左, backMoney, Ai.中括号右,
                        "失败,当前余额", Ai.中括号左, member.Balance,
                        Ai.中括号右));

                // 构造返回消息
                returnMsg += $"申请【下{backMoney}】失败,分数不足." + "\r";
                returnMsg += $"{CommonHelper.GetFaceIdMoneyBag()}:{Math.Round(member.Balance, 2)}" + "\r";
                return;
            }

            // 记录旧积分和新积分
            decimal old可用分值 = member.Balance;
            member.Balance -= backMoney;

            // 使用事务操作数据库
            using DbContext? dbContext = DbHelper.FSql.CreateDbContext();

            // 创建下分申请订单
            SubMoney subMoney = new SubMoney
            {
                Account = member.Account,
                Money = backMoney,
                Status = EnumBalanceStatus.等待处理,
                FromMsgId = msg.Id
            };
            await DbHelper.FSql.Insert<SubMoney>()
                .AppendData(subMoney)
                .ExecuteIdentityAsync();

            //记录到财务凭证
            Finance caiWu = new Finance
            {
                Account = member.Account,
                变动前 = old可用分值,
                变动值 = -backMoney,
                变动后 = member.Balance,
                凭据 = "用户申请下分",
                对应信息 = msg.Id
            };
            await DbHelper.FSql.Insert<Finance>()
                .AppendData(caiWu)
                .ExecuteIdentityAsync();

            // 更新会员信息
            await DbHelper.FSql.Update<Member>()
                .Set(a => a.Balance, member.Balance)
                .Where(a => a.Account == member.Account)
                .ExecuteAffrowsAsync();

            // 保存到数据库
            await dbContext.SaveChangesAsync();

            // 记录日志
            await DbHelper.AddLog(EnumLogType.机器人, "申请下分", string.Concat(Ai.中括号左, member.Account, Ai.小圆点, member.昵称, Ai.中括号右, "申请下分", Ai.中括号左, backMoney, Ai.中括号右, "已通过系统审核,等待处理."));

            // 构造返回消息
            StringBuilder returnMsgBuilder = new StringBuilder();
            returnMsgBuilder.Append($"已收到你{Ai.中括号左}{msg.Content}{Ai.中括号右}的申请，请等待审核." + "\r");
            returnMsgBuilder.Append($"{CommonHelper.GetFaceIdMoneyBag()}:{Math.Round(member.Balance, 2)}" + "\r");
            returnMsg = returnMsgBuilder.ToString();

            // 播放声音提醒
            SoundHelper.MediaRechargeWithdrawal.Play();
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "下分指令Handler", ex.ToString());
        }
        finally
        {
            await ChatHelper.SendGroupMessage(returnMsg, member.Account);
        }
    }

    #endregion

    #region 答题指令

    private static async Task 下注指令Handler(Member member, ReceiveMessage msg)
    {
        string returnMsg = string.Empty;
        try
        {
            // 处理换行符
            string msgCon = msg.Content
                .Replace(Environment.NewLine, " ")
                .Replace("\\r\\n", " ")
                .Replace("\\r", " ")
                .Replace("\\n", " ")
                .Replace("\r\n", " ")
                .Replace("\r", " ")
                .Replace("\n", " ")
                .Replace("  ", " ")
                .Trim();

            // 确定投注彩种
            EnumBetLottery betLottery = EnumBetLottery.台湾宾果2;
            if (CommonHelper.Lottery.Equals(EnumLottery.台湾宾果))
            {
                betLottery = EnumBetLottery.台湾宾果2;
            }
            else if (CommonHelper.Lottery.Equals(EnumLottery.一六八飞艇))
            {
                betLottery = EnumBetLottery.一六八飞艇前3;
            }
            else if (CommonHelper.Lottery.Equals(EnumLottery.新一六八XL))
            {
                betLottery = EnumBetLottery.新一六八XL前;
            }

            // 根据首字符确定投注类型
            string firstChar = msgCon.Substring(0, 1);
            if (CommonHelper.Lottery.Equals(EnumLottery.台湾宾果))
            {
                if (firstChar.Equals("前"))
                {
                    betLottery = EnumBetLottery.台湾宾果1;
                    if (Setting.Current.启用台湾宾果1 != 1)
                    {
                        returnMsg += $"{Ai.中括号左}宾果1{Ai.中括号右}未开放:" + "\r";
                        return;
                    }

                    msgCon = msgCon.Substring(1, msgCon.Length - 1);
                }
                else if (firstChar.Equals("后"))
                {
                    betLottery = EnumBetLottery.台湾宾果3;
                    if (Setting.Current.启用台湾宾果3 != 1)
                    {
                        returnMsg += $"{Ai.中括号左}宾果3{Ai.中括号右}未开放:" + "\r";
                        return;
                    }

                    msgCon = msgCon.Substring(1, msgCon.Length - 1);
                }
                else
                {
                    betLottery = EnumBetLottery.台湾宾果2;
                    if (Setting.Current.启用台湾宾果2 != 1)
                    {
                        returnMsg += $"{Ai.中括号左}宾果2{Ai.中括号右}未开放:" + "\r";
                        return;
                    }
                }
            }
            else if (CommonHelper.Lottery.Equals(EnumLottery.一六八飞艇))
            {
                if (firstChar.Equals("中"))
                {
                    betLottery = EnumBetLottery.一六八飞艇中3;
                    if (Setting.Current.启用168飞艇前3 != 1)
                    {
                        returnMsg += $"{Ai.中括号左}飞艇中3{Ai.中括号右}未开放:" + "\r";
                        return;
                    }

                    msgCon = msgCon.Substring(1, msgCon.Length - 1);
                }
                else if (firstChar.Equals("后"))
                {
                    betLottery = EnumBetLottery.一六八飞艇后3;
                    if (Setting.Current.启用168飞艇后3 != 1)
                    {
                        returnMsg += $"{Ai.中括号左}飞艇后3{Ai.中括号右}未开放:" + "\r";
                        return;
                    }

                    msgCon = msgCon.Substring(1, msgCon.Length - 1);
                }
                else
                {
                    betLottery = EnumBetLottery.一六八飞艇前3;
                    if (Setting.Current.启用168飞艇前3 != 1)
                    {
                        returnMsg += $"{Ai.中括号左}飞艇前3{Ai.中括号右}未开放:" + "\r";
                        return;
                    }
                }
            }
            else if (CommonHelper.Lottery.Equals(EnumLottery.新一六八XL))
            {
                if (firstChar.Equals("中"))
                {
                    betLottery = EnumBetLottery.新一六八XL中;
                    if (Setting.Current.启用新168XL中 != 1)
                    {
                        returnMsg += $"{Ai.中括号左}新168XL中{Ai.中括号右}未开放:" + "\r";
                        return;
                    }

                    msgCon = msgCon.Substring(1, msgCon.Length - 1);
                }
                else if (firstChar.Equals("后"))
                {
                    betLottery = EnumBetLottery.新一六八XL后;
                    if (Setting.Current.启用新168XL后 != 1)
                    {
                        returnMsg += $"{Ai.中括号左}新168XL后{Ai.中括号右}未开放:" + "\r";
                        return;
                    }

                    msgCon = msgCon.Substring(1, msgCon.Length - 1);
                }
                else
                {
                    betLottery = EnumBetLottery.新一六八XL前;
                    if (Setting.Current.启用新168XL前 != 1)
                    {
                        returnMsg += $"{Ai.中括号左}新168XL前{Ai.中括号右}未开放:" + "\r";
                        return;
                    }
                }
            }

            // 取出项目赔率
            List<Odds>? oddsList = await DbHelper.FSql.Select<Odds>().ToListAsync();

            // 分割指令
            string[] orders = Ai.Split(msgCon, " ");

            // 检测非法指令
            bool containsIllegalOrder = orders.Any(order =>
            {
                // 修正指令并去除前后空格
                string newOrder = ChangeOrder(order).Trim();

                // 检测是否是答题指令
                return !CommonHelper.PlayContentList.Any(playItem =>
                {
                    Match mc = Regex.Match(newOrder, $@"^{playItem}/\d{{1,}}$");
                    return mc.Success;
                });
            });

            // 判断是否包含非法指令
            if (containsIllegalOrder)
            {
                returnMsg = $"{Ai.五角星}答题错误,请检查指令格式{CommonHelper.GetFaceIdChaCha()}";
                return;
            }

            // 判断是否已开盘
            if (!OpenTipsList.Contains(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue))
            {
                // 构造返回消息
                StringBuilder returnMsgBuilder = new StringBuilder();
                returnMsgBuilder.Append($"{Ai.中括号左}{IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue}{Ai.中括号右}未开始答题{CommonHelper.GetFaceIdChaCha()}\r");
                returnMsgBuilder.Append("以下答题无效:\r");
                returnMsgBuilder.Append(msgCon + "\r");
                returnMsg = returnMsgBuilder.ToString();
                await DbHelper.AddLog(EnumLogType.机器人, "未开盘驳回用户答题申请", string.Concat(Ai.中括号左, member.Account, Ai.小圆点, member.昵称, Ai.中括号右, "[", msg.Id, "]"));
                return;
            }

            // 判断是否封盘状态
            if (CloseTipsList.Contains(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue))
            {
                StringBuilder returnMsgBuilder = new StringBuilder();
                returnMsgBuilder.Append($"{Ai.中括号左}{IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue}{Ai.中括号右}已封卷{CommonHelper.GetFaceIdChaCha()}\r");
                returnMsgBuilder.Append("以下答题无效\r");
                returnMsgBuilder.Append(msgCon + "\r");
                returnMsg = returnMsgBuilder.ToString();
                await DbHelper.AddLog(EnumLogType.机器人, "封盘驳回用户答题申请", string.Concat(Ai.中括号左, member.Account, Ai.小圆点, member.昵称, Ai.中括号右, "[", msg.Id, "]"));
                return;
            }

            // 遍历指令,计算投注总额
            decimal totalMoney = 0;
            foreach (string order in orders)
            {
                string orderNew = ChangeOrder(order);
                // 处理指令
                decimal money = Convert.ToDecimal(Ai.GetTextRight(orderNew, "/"));
                totalMoney += money;
            }

            // 判断是否有足够的积分,转换保留2位小数,避免浮点数精度问题
            if (Math.Round(member.Balance, 2) < Math.Round(totalMoney, 2))
            {
                // 构造返回消息
                returnMsg += $"余额不足{CommonHelper.GetFaceIdChaCha()}\r";
                returnMsg += $"合计需要{CommonHelper.GetFaceIdMoneyBag()}:{totalMoney}\r";
                returnMsg += $"当前可用{CommonHelper.GetFaceIdMoneyBag()}:{Math.Round(member.Balance, 2)}";
                return;
            }

            // 遍历指令,处理每一项投注
            bool haveNewBetData = false;
            foreach (string order in orders)
            {
                string orderNew = ChangeOrder(order);

                // 处理指令
                string? content = Ai.GetTextLeft(orderNew, "/");
                decimal money = Convert.ToDecimal(Ai.GetTextRight(orderNew, "/"));

                // 取出项目赔率
                Odds odds = oddsList.FirstOrDefault(item => item.项目 == content)!;

                // 判断是否低于最低投注限制
                if (Math.Round(money, 2) < Math.Round(odds.最低限投, 2))
                {
                    returnMsg += $"{returnMsg}【{order}】此项失败,单次最低限制{Ai.中括号左}{odds.最低限投}{Ai.中括号右}\r";
                    continue;
                }

                // 判断是否高于最高投注限制
                if (Math.Round(money, 2) > Math.Round(odds.最高限投, 2))
                {
                    returnMsg += $"{returnMsg}【{order}】此项失败,单次高于限制{Ai.中括号左}{odds.最低限投}{Ai.中括号右}.\r";
                    continue;
                }

                // 取出该项目当前已投总额
                decimal ttMoney = await DbHelper.FSql.Select<BetOrder>()
                    .Where(a => a.BetLottery == betLottery)
                    .Where(a => a.Issue == IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue)
                    .Where(a => a.Con == content)
                    .Where(a => a.BetOrderStatus == EnumBetOrderStatus.已受理)
                    .SumAsync(a => a.Money);

                // 判断是否超出总额限制
                if (ttMoney + money > odds.总额限投)
                {
                    returnMsg = $"{returnMsg}【{order}】此项失败,单次总额高于限制总分数.该项剩余可答题分数{Ai.中括号左}{odds.总额限投 - ttMoney}{Ai.中括号右}.\r";
                    continue;
                }

                // 扣除积分
                decimal old可用分值 = member.Balance;
                member.Balance -= money;

                try
                {
                    // 使用事务操作数据库
                    using DbContext? dbContext = DbHelper.FSql.CreateDbContext();

                    // 记录到下注表
                    BetOrder betOrder = new BetOrder
                    {
                        Account = member.Account,
                        Issue = IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue,
                        BetLottery = betLottery,
                        Con = content,
                        Money = money,
                        Odds = odds.赔率,
                        WinLose = EnumBetWinLose.未开奖,
                        BetOrderStatus = EnumBetOrderStatus.已受理,
                        BetOrderRebateStatus = EnumBetOrderRebateStatus.未回水,
                        FromMsgId = msg.Id,
                        OrderType = member.是否假人 ? EnumOrderType.假人订单 : EnumOrderType.真人订单
                    };

                    betOrder.Id = await DbHelper.FSql.Insert<BetOrder>()
                        .AppendData(betOrder)
                        .ExecuteIdentityAsync();

                    // 更新用户信息
                    await DbHelper.FSql.Update<Member>()
                        .Set(a => a.Balance, member.Balance)
                        .Where(a => a.Account == member.Account)
                        .ExecuteAffrowsAsync();

                    //记录财务凭证
                    Finance cw = new Finance
                    {
                        Account = member.Account,
                        变动前 = old可用分值,
                        变动值 = -money,
                        变动后 = member.Balance,
                        凭据 = "答题扣除",
                        对应信息 = $"{msg.Id}{Ai.中括号左}{order}{Ai.中括号右}{Ai.中括号左}{IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue}{Ai.中括号右}"
                    };
                    await DbHelper.FSql.Insert<Finance>()
                        .AppendData(cw)
                        .ExecuteIdentityAsync();

                    // 保存到数据库
                    await dbContext.SaveChangesAsync();
                    haveNewBetData = true;
                }
                catch (Exception ex)
                {
                    returnMsg += "执行答题出错,请管理员检查:" + order;
                    string tipsTitle = Ai.中括号左 + "处理答题指令出错" + Ai.中括号右;
                    string errorMessage = $"原始指令[{order}],修正后的指令[{orderNew}]错误信息:{ex}";
                    await DbHelper.AddLog(EnumLogType.机器人, tipsTitle, errorMessage);
                }
            }

            // 答题成功
            if (haveNewBetData)
            {
                returnMsg += $"{CommonHelper.GetFaceIdGreenDuiGou()}答题成功:\r";

                // 取出此账号本期所有已受理的投注
                List<BetOrder>? betOrderList = await DbHelper.FSql.Select<BetOrder, Member>()
                    .LeftJoin((a, b) => a.Account == b.Account)
                    .Where(t => t.t1.Account == member.Account)
                    .Where((a, b) => a.Issue == IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue)
                    .Where((a, b) => a.BetOrderStatus == EnumBetOrderStatus.已受理)
                    .ToListAsync();

                // 取出所有投注类型
                HashSet<EnumBetLottery> betLotteryList = [..betOrderList.Select(betOrder => betOrder.BetLottery)];
                betLotteryList = [..betLotteryList.OrderBy(enumBetLottery => (int)enumBetLottery)];

                // 遍历游戏类型
                foreach (EnumBetLottery lottery in betLotteryList)
                {
                    returnMsg += $"{Ai.中括号左}{lottery.ToString()}{Ai.中括号右}:\r";

                    // 拼接投注数据
                    Dictionary<string, decimal> 答题明细Dic = new();
                    foreach (string playType in CommonHelper.PlayContentList)
                    {
                        答题明细Dic.Add(playType, 0);
                    }

                    // 计算每项投注金额总和
                    foreach (BetOrder betOrder in betOrderList)
                    {
                        if (betOrder.BetLottery == lottery)
                        {
                            答题明细Dic[betOrder.Con] += betOrder.Money;
                        }
                    }

                    // 构造返回消息
                    foreach (KeyValuePair<string, decimal> kv in 答题明细Dic)
                    {
                        if (kv.Value > 0)
                        {
                            returnMsg += $"{kv.Key}/{kv.Value}\r";
                        }
                    }
                }

                returnMsg += $"{CommonHelper.GetFaceIdMoneyBag()}:{Math.Round(member.Balance, 2)}\r";
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "下注指令Handler", ex.ToString());
        }
        finally
        {
            await ChatHelper.SendGroupMessage(returnMsg, member.Account);
        }
    }

    #endregion

    #region 修正答题指令

    /// <summary>
    /// ChangeOrder修正下注指令
    /// </summary>
    /// <param name="order"></param>
    /// <returns></returns>
    private static string ChangeOrder(string order)
    {
        order = order.Replace(".", "/");
        order = order.Replace("。", "/");
        order = order.Replace("，", "/");
        order = order.Replace(",", "/");
        order = order.Replace("?", "/");
        order = order.Replace("=", "/");
        order = order.Replace("+", "/");
        order = order.Replace("·", "/");
        order = order.Replace("！", "/");
        order = order.Replace("!", "/");
        order = order.Replace("@", "/");
        order = order.Replace("#", "/");
        order = order.Replace("*", "/");
        order = order.Replace("、", "/");
        order = order.Replace("／", "/");
        order = order.Replace("正", "正/");
        order = order.Replace("堂", "正/");
        order = order.Replace("糖", "正/");
        order = order.Replace("中", "正/");
        order = order.Replace("番", "番/");
        order = order.Replace("高", "番/");
        order = order.Replace("牛", "番/");
        order = order.Replace("风", "番/");
        order = order.Replace("方", "番/");
        order = order.Replace("翻", "番/");
        order = order.Replace(":", "番/");
        order = order.Replace("角", "角/");
        order = order.Replace("粘", "念");
        order = order.Replace("贴", "念");
        order = order.Replace("严", "念");
        order = order.Replace("那", "念");
        order = order.Replace("拿", "念");
        order = order.Replace("拖", "念");
        order = order.Replace("-", "念");
        if (order.Contains("念") && order.Length > 4)
        {
            order = order.Substring(0, 3) + "/" + order.Substring(3, order.Length - 3);
        }

        order = order.Replace("单", "单/");
        order = order.Replace("双", "双/");
        order = order.Replace("大", "大/");
        order = order.Replace("小", "小/");
        order = order.Replace("//", "/");

        if (Ai.GetTextLeft(order, "/") == "1") order = order.Replace("1/", "1正/");
        if (Ai.GetTextLeft(order, "/") == "2") order = order.Replace("2/", "2正/");
        if (Ai.GetTextLeft(order, "/") == "3") order = order.Replace("3/", "3正/");
        if (Ai.GetTextLeft(order, "/") == "4") order = order.Replace("4/", "4正/");
        if (Ai.GetTextLeft(order, "/") == "12") order = order.Replace("12/", "12角/");
        if (Ai.GetTextLeft(order, "/") == "21") order = order.Replace("21/", "12角/");
        if (Ai.GetTextLeft(order, "/") == "23") order = order.Replace("23/", "23角/");
        if (Ai.GetTextLeft(order, "/") == "32") order = order.Replace("32/", "23角/");
        if (Ai.GetTextLeft(order, "/") == "34") order = order.Replace("34/", "34角/");
        if (Ai.GetTextLeft(order, "/") == "43") order = order.Replace("43/", "34角/");
        if (Ai.GetTextLeft(order, "/") == "14") order = order.Replace("14/", "14角/");
        if (Ai.GetTextLeft(order, "/") == "41") order = order.Replace("41/", "14角/");
        if (Ai.GetTextLeft(order, "/") == "31") order = order.Replace("31/", "单/");
        if (Ai.GetTextLeft(order, "/") == "13") order = order.Replace("13/", "单/");
        if (Ai.GetTextLeft(order, "/") == "24") order = order.Replace("24/", "双/");
        if (Ai.GetTextLeft(order, "/") == "42") order = order.Replace("42/", "双/");
        if (Ai.GetTextLeft(order, "/") == "1122") order = order.Replace("1122/", "小/");
        if (Ai.GetTextLeft(order, "/") == "2211") order = order.Replace("2211/", "小/");
        if (Ai.GetTextLeft(order, "/") == "3344") order = order.Replace("3344/", "大/");
        if (Ai.GetTextLeft(order, "/") == "4433") order = order.Replace("4433/", "大/");
        if (Ai.GetTextLeft(order, "/") == "123") order = order.Replace("123/", "三门123/");
        if (Ai.GetTextLeft(order, "/") == "124") order = order.Replace("124/", "三门124/");
        if (Ai.GetTextLeft(order, "/") == "134") order = order.Replace("134/", "三门134/");
        if (Ai.GetTextLeft(order, "/") == "234") order = order.Replace("234/", "三门234/");
        if (Ai.GetTextLeft(order, "/") == "152") order = order.Replace("152/", "1无2/");
        if (Ai.GetTextLeft(order, "/") == "153") order = order.Replace("153/", "1无3/");
        if (Ai.GetTextLeft(order, "/") == "154") order = order.Replace("154/", "1无4/");
        if (Ai.GetTextLeft(order, "/") == "251") order = order.Replace("251/", "2无1/");
        if (Ai.GetTextLeft(order, "/") == "253") order = order.Replace("253/", "2无3/");
        if (Ai.GetTextLeft(order, "/") == "254") order = order.Replace("254/", "2无4/");
        if (Ai.GetTextLeft(order, "/") == "351") order = order.Replace("351/", "3无1/");
        if (Ai.GetTextLeft(order, "/") == "352") order = order.Replace("352/", "3无2/");
        if (Ai.GetTextLeft(order, "/") == "354") order = order.Replace("354/", "3无4/");
        if (Ai.GetTextLeft(order, "/") == "451") order = order.Replace("451/", "4无1/");
        if (Ai.GetTextLeft(order, "/") == "452") order = order.Replace("452/", "4无2/");
        if (Ai.GetTextLeft(order, "/") == "453") order = order.Replace("453/", "4无3/");

        return order;
    }

    #endregion

    // ##################################

    #region 发送开盘提醒

    /// <summary>
    /// 发送开盘提醒
    /// </summary>
    /// <param name="issue"></param>
    public static async Task 发送开盘提醒(string issue)
    {
        string returnMsg = string.Empty;
        returnMsg = string.Concat(returnMsg, Ai.中括号左, issue, Ai.中括号右, "开★始★答★题");
        await ChatHelper.SendGroupMessage(returnMsg);
    }

    #endregion

    #region 发送封盘倒计时提醒

    /// <summary>
    /// 发送封盘倒计时提醒
    /// </summary>
    /// <param name="issue"></param>
    public static async Task 发送封盘倒计时提醒(string issue)
    {
        string returnMsg = string.Empty;
        returnMsg = string.Concat(returnMsg, Ai.中括号左, issue, Ai.中括号右, "\r", "----封卷时间还剩 ", (IssueTimeHelper.CloseTimeDownDic[CommonHelper.Lottery] - Setting.Current.封盘时间).ToString(), " 秒----");
        await ChatHelper.SendGroupMessage(returnMsg);
    }

    #endregion

    #region 发送封盘提醒

    /// <summary>
    /// 发送封盘提醒
    /// </summary>
    /// <param name="betOrderList"></param>
    /// <returns></returns>
    public static async Task 发送封盘提醒(List<BetOrder> betOrderList)
    {
        try
        {
            // 拼接内容
            string returnMsg = $"当前时间:{Ai.中括号左}{DateTime.Now:HH:mm:ss}{Ai.中括号右}" + "\r";
            returnMsg += $"停止答题{Ai.中括号左}{IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue}{Ai.中括号右}";

            // 从betOrderList中取出所有参与答题的成员
            Dictionary<string, decimal> memberBetMoneyDic = new Dictionary<string, decimal>();
            foreach (BetOrder betOrder in betOrderList)
            {
                memberBetMoneyDic.TryAdd(betOrder.Account, 0);
                memberBetMoneyDic[betOrder.Account] += betOrder.Money;
            }

            // 使用 LINQ 查询表达式对字典进行排序,按金额排序
            IOrderedEnumerable<KeyValuePair<string, decimal>> betMemberDic = from entry in memberBetMoneyDic orderby entry.Key select entry;

            // 构造返回消息
            returnMsg += "\r" + "——————————" + "\r";
            returnMsg += $"答题人数: {Ai.中括号左}{memberBetMoneyDic.Count}{Ai.中括号右}" + "\r";
            returnMsg += $"答题总分: {Ai.中括号左}{memberBetMoneyDic.Sum(a => a.Value)}{Ai.中括号右}" + "\r";

            // 拼接每个人的投注数据
            foreach (KeyValuePair<string, decimal> kv in betMemberDic)
            {
                // 取出用户信息
                Member member = await DbHelper.FSql.Select<Member>()
                    .Where(p => p.Account == kv.Key)
                    .FirstAsync();

                // 计算该成员投注金额
                returnMsg += string.Concat("\r", Ai.菱形, " ", Ai.中括号左, member.备注名, Ai.中括号右, CommonHelper.GetFaceIdMoneyBag(), ":", kv.Value);

                // 提取个人数据
                List<BetOrder> tmpBetOrderList = betOrderList.Where(p => p.Account == member.Account).ToList();
                HashSet<EnumBetLottery> betLotteryList = [..tmpBetOrderList.Select(betOrder => betOrder.BetLottery)];
                betLotteryList = [..betLotteryList.OrderBy(enumBetLottery => (int)enumBetLottery)];

                // 遍历游戏类型
                foreach (EnumBetLottery betLottery in betLotteryList)
                {
                    // 判断tmpBetOrderList中是否有该游戏类型的数据
                    if (tmpBetOrderList.All(betOrder => betOrder.BetLottery != betLottery))
                    {
                        continue;
                    }

                    returnMsg += "\r" + "\r" + Ai.中括号左 + betLottery + Ai.中括号右 + ":\r";

                    // 取出该成员的投注数据
                    Dictionary<string, decimal> betDataDic = new();
                    foreach (BetOrder order in tmpBetOrderList)
                    {
                        if (order.BetLottery == betLottery)
                        {
                            betDataDic.TryAdd(order.Con, 0);
                            betDataDic[order.Con] += order.Money;
                        }
                    }

                    // 拼接数据
                    int count = 0;
                    foreach (KeyValuePair<string, decimal> ky in betDataDic)
                    {
                        if (ky.Value > 0)
                        {
                            returnMsg += "[" + ky.Key + "/" + ky.Value + "]";
                            returnMsg += " ";

                            // 每3个后或没有后换行
                            count++;
                            if (count % 3 == 0 || count.Equals(betDataDic.Count))
                            {
                                returnMsg += "\r";
                            }
                        }
                    }
                }
            }

            returnMsg += "\r" + "——————————" + "\r";
            returnMsg += $"停止答题{Ai.中括号左}{IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue}{Ai.中括号右}" + "\r";
            returnMsg += "因各种不可抗力因素造成的问题，为避免争执，一切以系统端所录入数据为准，请知悉。若答题后没有及时收到回复,可发送数字1核查。";

            // 发送群提醒
            await ChatHelper.SendGroupMessage(returnMsg);

            // 保存封盘数据
            CloseTimeBetReport report = new()
            {
                Issue = IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue,
                Report = returnMsg
            };
            await DbHelper.FSql.Insert<CloseTimeBetReport>()
                .AppendData(report)
                .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
            throw;
        }
    }

    #endregion

    #region 处理汇总数据

    /// <summary>
    /// 处理汇总数据
    /// </summary>
    /// <param name="betOrderList"></param>
    /// <returns></returns>
    public static async Task 处理汇总数据(List<BetOrder> betOrderList)
    {
        string issue = IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue;
        HashSet<EnumBetLottery> betLotteryList = [..betOrderList.Select(betOrder => betOrder.BetLottery)];
        betLotteryList = [..betLotteryList.OrderBy(enumBetLottery => (int)enumBetLottery)];

        // 遍历betLotteryList
        foreach (EnumBetLottery betLottery in betLotteryList)
        {
            // 过滤取出所有正常玩家的答题项目和金额
            Dictionary<string, decimal> betDataDic = betOrderList
                .Where(betOrder => betOrder.OrderType == EnumOrderType.真人订单 && betOrder.BetLottery == betLottery)
                .GroupBy(betOrder => betOrder.Con)
                .ToDictionary(group => group.Key, group => group.Sum(betOrder => betOrder.Money));

            // 插入数据库
            foreach (KeyValuePair<string, decimal> contentBalance in betDataDic)
            {
                // 从odds表中取出赔率
                decimal odds = (await DbHelper.FSql.Select<Odds>()
                    .Where(a => a.项目.Equals(contentBalance.Key))
                    .ToOneAsync()).赔率;

                // 创建新对象
                HuiZong hz = new HuiZong
                {
                    BetLottery = betLottery, // 类型
                    Issue = issue, // 期号
                    Content = contentBalance.Key, // 项目内容
                    TotalBalance = contentBalance.Value, // 总金额
                    ToBetBalance = contentBalance.Value, // 待下注金额
                    赔率 = odds, // 赔率
                };

                // 保存到数据库
                hz.Id = await DbHelper.FSql.Insert<HuiZong>()
                    .AppendData(hz)
                    .ExecuteAffrowsAsync();
            }
        }
    }

    #endregion

    #region 处理对冲吃单

    /// <summary>
    /// 处理对冲吃单
    /// </summary>
    public static async Task 处理对冲吃单()
    {
        try
        {
            // 判断是否开启对冲吃单
            if (!Setting.Current.是否对冲吃单)
            {
                return;
            }

            string issue = IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue;

            // 提取汇总数据
            List<HuiZong> hzList = await DbHelper.FSql.Select<HuiZong>()
                .Where(a => a.Issue.Equals(issue))
                .ToListAsync();

            // 提取游戏类型
            HashSet<EnumBetLottery> betLotteryList = [..hzList.Select(betOrder => betOrder.BetLottery)];
            betLotteryList = [..betLotteryList.OrderBy(enumBetLottery => (int)enumBetLottery)];

            // 遍历betLotteryList
            foreach (EnumBetLottery betLottery in betLotteryList)
            {
                // 将指定类型的数据转换为字典
                Dictionary<string, decimal> betDataDic = hzList
                    .Where(hz => hz.BetLottery == betLottery)
                    .ToDictionary(hz => hz.Content, hz => hz.ToBetBalance);

                // 遍历所有对冲项目
                for (int i = 1; i <= 7; i++)
                {
                    // 提取对冲项目
                    List<string> playContentList = new();
                    switch (i)
                    {
                        case 1:
                            playContentList.Add("1正");
                            playContentList.Add("3正");
                            break;
                        case 2:
                            playContentList.Add("2正");
                            playContentList.Add("4正");
                            break;
                        case 3:
                            playContentList.Add("1番");
                            playContentList.Add("2番");
                            playContentList.Add("3番");
                            playContentList.Add("4番");
                            break;
                        case 4:
                            playContentList.Add("12角");
                            playContentList.Add("34角");
                            break;
                        case 5:
                            playContentList.Add("23角");
                            playContentList.Add("14角");
                            break;
                        case 6:
                            playContentList.Add("1念2");
                            playContentList.Add("1念3");
                            playContentList.Add("1念4");
                            playContentList.Add("2念1");
                            playContentList.Add("2念3");
                            playContentList.Add("2念4");
                            playContentList.Add("3念1");
                            playContentList.Add("3念2");
                            playContentList.Add("3念4");
                            playContentList.Add("4念1");
                            playContentList.Add("4念2");
                            playContentList.Add("4念3");
                            break;
                        case 7:
                            playContentList.Add("单");
                            playContentList.Add("双");
                            break;
                    }

                    // 第1,判断所有对冲项目都有金额
                    bool allPlayTypeHasBetMoney = playContentList.All(content => betDataDic.ContainsKey(content) && betDataDic[content] > 0);
                    if (!allPlayTypeHasBetMoney)
                    {
                        continue;
                    }

                    // 第2,判断所有对冲项目金额是否相等
                    bool allPlayTypeMoneyIsEqual = playContentList.All(content => betDataDic[content].Equals(betDataDic[playContentList[0]]));
                    if (allPlayTypeMoneyIsEqual)
                    {
                        foreach (string content in playContentList)
                        {
                            //更新到数据库
                            await DbHelper.FSql.Update<HuiZong>()
                                .Set(a => a.EatBalance, betDataDic[content])
                                .Set(a => a.ToBetBalance, 0)
                                .Where(a => a.BetLottery == betLottery)
                                .Where(a => a.Issue == issue)
                                .Where(a => a.Content == content)
                                .ExecuteAffrowsAsync();
                        }

                        continue;
                    }

                    // 第3,找出最小金额
                    decimal minMoney = decimal.MaxValue;
                    foreach (string content in playContentList)
                    {
                        if (minMoney > betDataDic[content])
                        {
                            minMoney = betDataDic[content];
                        }
                    }

                    // 第4,预判吃单后的金额不低于最低飞单金额
                    bool allContentMoneyIsOk = playContentList.All(content =>
                        betDataDic[content].Equals(minMoney) ||
                        betDataDic[content] - minMoney >= Setting.Current.最低飞单金额);

                    // 吃单后所有金额都符合要求
                    if (allContentMoneyIsOk)
                    {
                        foreach (string content in playContentList)
                        {
                            betDataDic[content] -= minMoney;

                            //更新到数据库
                            await DbHelper.FSql.Update<HuiZong>()
                                .Set(a => a.EatBalance, minMoney)
                                .Set(a => a.ToBetBalance, betDataDic[content])
                                .Where(a => a.BetLottery == betLottery)
                                .Where(a => a.Issue == issue)
                                .Where(a => a.Content == content)
                                .ExecuteAffrowsAsync();
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "处理对冲吃单", ex.ToString());
        }
    }

    #endregion

    #region 结算答题数据

    /// <summary>
    /// 结算答题数据
    /// </summary>
    public static async Task 结算答题数据()
    {
        try
        {
            // 调取所有状态为已受理的期号
            IEnumerable<string> issueList = DbHelper.FSql.Select<BetOrder>()
                .Where(a => a.BetOrderStatus == EnumBetOrderStatus.已受理)
                .ToList(a => a.Issue)
                .Distinct();

            //遍历所有未结算期号进行结算
            foreach (string issue in issueList)
            {
                // 从数据库取出开奖数据
                KaiJiang? kj = await DbHelper.FSql.Select<KaiJiang>()
                    .Where(a => a.Issue.Equals(issue))
                    .ToOneAsync();

                if (kj != null)
                {
                    结算指定期号数据(kj);
                    if (Setting.Current.自动回水)
                    {
                        await RebateHandler();
                    }

                    发送结算汇总报告(kj);
                    await Task.Delay(500);
                    SendMemberInfo();
                }
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, Ai.中括号左 + "事件" + Ai.中括号右 + "结算错误", ex.ToString());
        }
    }

    #endregion

    #region 结算指定期号数据(KaiJiang kj)

    /// <summary>
    /// 结算指定期号数据(KaiJiang kj)
    /// </summary>
    /// <param name="kj"></param>
    private static async void 结算指定期号数据(KaiJiang kj)
    {
        try
        {
            await DbHelper.AddLog(EnumLogType.机器人, "开始结算答题数据", $"结算期号{Ai.中括号左}{kj.Issue}{Ai.中括号右}");

            // 联表查询BetOrder,Member,取出指定期号的数据并且根据Account分组去重
            List<BetOrder>? betOrderList = await DbHelper.FSql.Select<BetOrder>()
                .Where(t => t.Issue == kj.Issue)
                .Where(t => t.BetOrderStatus == EnumBetOrderStatus.已受理)
                .ToListAsync();

            // 遍历未结算数据
            foreach (BetOrder betOrder in betOrderList)
            {
                // 根据对应游戏获取开奖结果
                int drawResult = await GetDrawResult(kj, betOrder.BetLottery);
                EnumBetWinLose winLose = 判断项目中挂(betOrder.Con, drawResult).Result;

                // 根据中奖结果并结算金额
                decimal settlementMoney = 0;
                switch (winLose)
                {
                    case EnumBetWinLose.中:
                        settlementMoney = betOrder.Money * betOrder.Odds;
                        break;
                    case EnumBetWinLose.和:
                        settlementMoney = betOrder.Money;
                        break;
                    case EnumBetWinLose.挂:
                        settlementMoney = -betOrder.Money;
                        break;
                }

                // 更新BetOrder数据表
                await DbHelper.FSql.Update<BetOrder>()
                    .Set(a => a.DrawResult, drawResult)
                    .Set(a => a.WinLose, winLose)
                    .Set(a => a.结算, Math.Round(settlementMoney, 2))
                    .Set(a => a.BetOrderStatus, EnumBetOrderStatus.已结算)
                    .Where(a => a.Id == betOrder.Id)
                    .ExecuteAffrowsAsync();

                // 如果有进账,则更新Member数据表可用分值
                if (settlementMoney > 0)
                {
                    Member member = await DbHelper.GetMember(betOrder.Account);
                    await DbHelper.FSql.Update<Member>()
                        .Set(a => a.Balance, Math.Round(member.Balance + settlementMoney, 2))
                        .Where(a => a.Account == betOrder.Account)
                        .ExecuteAffrowsAsync();

                    // 记录财务凭据
                    if (settlementMoney > 0)
                    {
                        Finance fn = new Finance
                        {
                            Account = member.Account,
                            变动前 = Math.Round(member.Balance, 2),
                            变动值 = Math.Round(settlementMoney, 2),
                            变动后 = Math.Round(member.Balance + settlementMoney, 2),
                            凭据 = "结算进账",
                            对应信息 = $"{betOrder.FromMsgId}{Ai.中括号左}{betOrder.Con}/{betOrder.Money}{Ai.中括号右}"
                        };

                        await DbHelper.FSql.Insert<Finance>()
                            .AppendData(fn)
                            .ExecuteIdentityAsync();
                    }
                }
            }

            await DbHelper.AddLog(EnumLogType.机器人, "完成结算答题数据", $"结算期号{Ai.中括号左}{kj.Issue}{Ai.中括号右}");
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, Ai.中括号左 + "事件" + Ai.中括号右 + "结算指定期号数据错误" + ":", ex.ToString());
        }
    }

    #endregion

    #region 处理自动回水

    public static async Task RebateHandler()
    {
        try
        {
            // 取出可回水的参与人员
            List<string>? tmpList = await DbHelper.FSql.Select<BetOrder>()
                .Where(a => (a.WinLose == EnumBetWinLose.中 || a.WinLose == EnumBetWinLose.挂) && a.BetOrderRebateStatus == EnumBetOrderRebateStatus.未回水)
                .ToListAsync(a => a.Account);
            IEnumerable<string> memberList = tmpList.Distinct();

            // 遍历参与者,处理回水
            foreach (string account in memberList)
            {
                Member member = await DbHelper.GetMember(account);
                await RebateHandler(member, new ReceiveMessage());
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "AutoRebateHandlerError", ex.ToString());
        }
    }

    #endregion

    #region 发送结算汇总报告(KaiJiang kj)

    /// <summary>
    /// 发送结算汇总报告(KaiJiang kj)
    /// </summary>
    /// <param name="kj"></param>
    private static async void 发送结算汇总报告(KaiJiang kj)
    {
        try
        {
            await DbHelper.AddLog(EnumLogType.机器人, "开始整理结算汇总报告", "开始整理结算汇总报告");

            // 取出当期所有答题已结算记录
            List<BetOrder>? betOrderList = await DbHelper.FSql.Select<BetOrder>()
                .Where(t => t.Issue == kj.Issue)
                .Where(t => t.BetOrderStatus == EnumBetOrderStatus.已结算)
                .ToListAsync();

            // 提取所有参与者
            IEnumerable<string> memberList = betOrderList.Select(a => a.Account).Distinct();

            // 定义参与者总盈亏字典
            Dictionary<string, decimal> memberTotalWinMoneyDic = new();
            foreach (string account in memberList)
            {
                // 计算盈亏
                decimal winMoney = betOrderList.Where(a => a.Account == account)
                                       .Where(a => a.WinLose == EnumBetWinLose.中) // 累计所有中奖的金额
                                       .Sum(a => a.结算)
                                   - betOrderList.Where(a => a.Account == account)
                                       .Where(a => a.WinLose == EnumBetWinLose.中 || a.WinLose == EnumBetWinLose.挂) // 累计所有中奖的金额
                                       .Sum(a => a.Money);

                // 添加到字典,保留两位小数
                memberTotalWinMoneyDic.Add(account, Math.Round(winMoney, 2));
            }

            // 按盈亏排名
            IOrderedEnumerable<KeyValuePair<string, decimal>> sortedDict = from entry in memberTotalWinMoneyDic orderby entry.Key descending select entry;

            // 构造返回消息
            string returnMsg = $"当前时间{Ai.中括号左}{DateTime.Now:HH:mm:ss}{Ai.中括号右}\r";
            returnMsg += $"答案公布{Ai.中括号左}{kj.Issue}{Ai.中括号右}\r";
            returnMsg += $"答题人数{Ai.中括号左}{memberTotalWinMoneyDic.Count}{Ai.中括号右}\r";
            returnMsg += $"本题结算{Ai.中括号左}{Math.Round(memberTotalWinMoneyDic.Sum(a => a.Value), 2)}{Ai.中括号右}\r";

            if (CommonHelper.Lottery.Equals(EnumLottery.台湾宾果))
            {
                if (Setting.Current.启用台湾宾果1 == 1)
                {
                    string drawNum = await GetDrawNum(kj, EnumBetLottery.台湾宾果1);
                    int drawResult = await GetDrawResult(kj, EnumBetLottery.台湾宾果1);
                    returnMsg += $"宾果1:{Ai.中括号左}{drawNum}{Ai.中括号右} {Ai.中括号左}{drawResult}{Ai.中括号右} 摊\r";
                }

                if (Setting.Current.启用台湾宾果2 == 1)
                {
                    string drawNum = await GetDrawNum(kj, EnumBetLottery.台湾宾果2);
                    int drawResult = await GetDrawResult(kj, EnumBetLottery.台湾宾果2);
                    returnMsg += $"宾果2:{Ai.中括号左}{drawNum}{Ai.中括号右} {Ai.中括号左}{drawResult}{Ai.中括号右} 摊\r";
                }

                if (Setting.Current.启用台湾宾果3 == 1)
                {
                    string drawNum = await GetDrawNum(kj, EnumBetLottery.台湾宾果3);
                    int drawResult = await GetDrawResult(kj, EnumBetLottery.台湾宾果3);
                    string[] nums = drawNum.Split(',');
                    int sum = 0;
                    foreach (string num in nums)
                    {
                        sum += int.Parse(num);
                    }

                    returnMsg += $"宾果3:{Ai.中括号左}{sum}{Ai.中括号右}{Ai.中括号左}{drawResult}{Ai.中括号右} 摊\r";
                }
            }
            else if (CommonHelper.Lottery.Equals(EnumLottery.一六八飞艇))
            {
                if (Setting.Current.启用168飞艇前3 == 1)
                {
                    string drawNum = await GetDrawNum(kj, EnumBetLottery.一六八飞艇前3);
                    int drawResult = await GetDrawResult(kj, EnumBetLottery.一六八飞艇前3);
                    returnMsg += $"168前3:{Ai.中括号左}{drawNum}{Ai.中括号右} {Ai.中括号左}{drawResult}{Ai.中括号右} 摊\r";
                }

                if (Setting.Current.启用168飞艇中3 == 1)
                {
                    string drawNum = await GetDrawNum(kj, EnumBetLottery.一六八飞艇中3);
                    int drawResult = await GetDrawResult(kj, EnumBetLottery.一六八飞艇中3);
                    returnMsg += $"168中3:{Ai.中括号左}{drawNum}{Ai.中括号右} {Ai.中括号左}{drawResult}{Ai.中括号右} 摊\r";
                }

                if (Setting.Current.启用168飞艇后3 == 1)
                {
                    string drawNum = await GetDrawNum(kj, EnumBetLottery.一六八飞艇后3);
                    int drawResult = await GetDrawResult(kj, EnumBetLottery.一六八飞艇后3);
                    returnMsg += $"168后3:{Ai.中括号左}{drawNum}{Ai.中括号右} {Ai.中括号左}{drawResult}{Ai.中括号右} 摊\r";
                }
            }

            returnMsg += "——————————————" + "\r";

            // 按降序遍历参与者, 拼接盈亏明细
            foreach (KeyValuePair<string, decimal> memberInfo in sortedDict)
            {
                string account = memberInfo.Key;
                string nickName = DbHelper.GetMember(account).Result.备注名;
                returnMsg += $"{CommonHelper.GetFaceIdStar()} {nickName} :{Ai.中括号左}{memberInfo.Value}{Ai.中括号右}\r";

                // 取出该参与者所有订单,并按游戏类型分组
                List<BetOrder> orderList = betOrderList.Where(a => a.Account == account).ToList();
                HashSet<EnumBetLottery> betLotteryList = [..orderList.Select(betOrder => betOrder.BetLottery)];
                betLotteryList = [..betLotteryList.OrderBy(enumBetLottery => (int)enumBetLottery)];

                // 遍历游戏类型,拼接金额明细
                foreach (EnumBetLottery betLottery in betLotteryList)
                {
                    returnMsg += $"{Ai.中括号左}{betLottery.ToString()}{Ai.中括号右}:" + "\r";

                    // 提取投注金额和中奖金额
                    Dictionary<string, decimal> betContentMoneyDic = new();
                    Dictionary<string, decimal> betContentWinMoneyDic = new();

                    // 遍历订单,累计投注金额和中奖金额
                    foreach (BetOrder order in orderList)
                    {
                        if (order.BetLottery == betLottery)
                        {
                            // 累计投注金额
                            betContentMoneyDic.TryAdd(order.Con, 0);
                            betContentMoneyDic[order.Con] += order.Money;

                            // 累计中奖金额
                            betContentWinMoneyDic.TryAdd(order.Con, 0);
                            betContentWinMoneyDic[order.Con] += order.结算;
                        }
                    }

                    // 拼接金额明细
                    foreach (KeyValuePair<string, decimal> kv in betContentMoneyDic)
                    {
                        returnMsg += $"{kv.Key}[{kv.Value}] = {Math.Round(betContentWinMoneyDic[kv.Key], 2)}\r";
                    }

                    returnMsg += "\r";
                }
            }

            returnMsg += "——————————————" + "\r";

            // 发送结算报告
            await ChatHelper.SendGroupMessage(returnMsg);
            await DbHelper.AddLog(EnumLogType.机器人, "完成整理结算汇总报告", "完成整理结算汇总报告");

            // 保存结算报告
            SettleReport settleReport = new()
            {
                Issue = kj.Issue,
                Report = returnMsg
            };
            await DbHelper.FSql.Insert<SettleReport>()
                .AppendData(settleReport)
                .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "整理结算汇总报告出错", ex.ToString());
        }
    }

    #endregion

    #region 发送每个人当前积分

    /// <summary>
    /// 发送每个人当前积分
    /// 有3除地方会调用这个方法
    /// 1.结算后
    /// 2.管理取消订单后
    /// 3.清除数据前
    /// </summary>
    public static async void SendMemberInfo()
    {
        try
        {
            string returnMsg = string.Empty;
            try
            {
                // 取出所有会员信息
                List<Member> memberList = await DbHelper.FSql.Select<Member>()
                    .Where(a => a.Balance >= 1) // 发送个人积分时，筛选出有积分的会员
                    .OrderByDescending(a => a.Balance)
                    .ToListAsync();
                decimal totalMoney = Math.Round(memberList.Sum(a => a.Balance), 2);

                returnMsg += $"答题人数:{Ai.中括号左}{memberList.Count}{Ai.中括号右}\r";
                returnMsg += $"答题总分:{Ai.中括号左}{totalMoney}{Ai.中括号右}\r";
                foreach (Member member in memberList)
                {
                    returnMsg += $"{CommonHelper.GetFaceIdStar()}{member.昵称}{CommonHelper.GetFaceIdMoneyBag()}:{Math.Round(member.Balance, 2)}\r";
                }
            }
            catch (Exception ex)
            {
                await DbHelper.AddLog(EnumLogType.机器人, "SendMemberInfoError", ex.ToString());
            }
            finally
            {
                await ChatHelper.SendGroupMessage(returnMsg);
            }
        }
        catch (Exception e)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "SendMemberInfoError", e.ToString());
        }
    }

    #endregion

    #region 结算飞单数据

    /// <summary>
    /// 结算飞单数据
    /// </summary>
    public static async Task 结算飞单数据()
    {
        try
        {
            // 调取所有未结算飞单期号
            IEnumerable<string> issueList = DbHelper.FSql.Select<HuiZong>()
                .Where(a => a.开奖结果 == 0)
                .ToList(a => a.Issue)
                .Distinct();

            //遍历所有未结算期号进行结算
            foreach (string issue in issueList)
            {
                // 从数据库取出开奖数据
                KaiJiang? kj = await DbHelper.FSql.Select<KaiJiang>()
                    .Where(a => a.Issue.Equals(issue))
                    .ToOneAsync();

                if (kj != null)
                {
                    List<HuiZong>? hzList = await DbHelper.FSql.Select<HuiZong>()
                        .Where(a => a.Issue.Equals(issue))
                        .Where(a => a.开奖结果 == 0)
                        .ToListAsync();

                    // 遍历未结算数据
                    foreach (HuiZong hz in hzList)
                    {
                        int drawResult = await GetDrawResult(kj, hz.BetLottery);

                        hz.开奖结果 = drawResult;
                        EnumBetWinLose winLose = 判断项目中挂(hz.Content, drawResult).Result;
                        if (winLose == EnumBetWinLose.中)
                        {
                            hz.吃单结算 = hz.EatBalance * hz.赔率;
                            hz.飞单结算 = hz.ToBetBalance * hz.赔率;
                        }
                        else if (winLose == EnumBetWinLose.挂)
                        {
                            hz.吃单结算 -= hz.EatBalance;
                            hz.飞单结算 -= hz.ToBetBalance;
                        }
                        else if (winLose == EnumBetWinLose.和)
                        {
                            hz.吃单结算 = hz.EatBalance;
                            hz.飞单结算 = hz.ToBetBalance;
                        }

                        // 更新数据库
                        await DbHelper.FSql.Update<HuiZong>()
                            .Set(a => a.开奖结果, hz.开奖结果)
                            .Set(a => a.吃单结算, Math.Round(hz.吃单结算, 2))
                            .Set(a => a.飞单结算, Math.Round(hz.飞单结算, 2))
                            .Where(a => a.Id == hz.Id)
                            .ExecuteAffrowsAsync();
                    }
                }
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, Ai.中括号左 + "事件" + Ai.中括号右 + "结算错误", ex.ToString());
        }
    }

    #endregion

    #region 判断项目中挂

    /// <summary>
    /// 判断项目中挂
    /// </summary>
    /// <param name="playType"></param>
    /// <param name="resNum"></param>
    /// <returns></returns>
    private static async Task<EnumBetWinLose> 判断项目中挂(string playType, int resNum)
    {
        try
        {
            if (playType.Contains("番"))
            {
                return Convert.ToInt32(Ai.GetTextLeft(playType, "番")) == resNum ? EnumBetWinLose.中 : EnumBetWinLose.挂;
            }

            if (playType.Contains("正"))
            {
                // 中
                int con = Convert.ToInt32(Ai.GetTextLeft(playType, "正"));
                if (resNum == con)
                {
                    return EnumBetWinLose.中;
                }

                // 挂和
                return Math.Abs(resNum - con).Equals(2) ? EnumBetWinLose.挂 : EnumBetWinLose.和;
            }

            if (playType.Contains("角"))
            {
                return playType.Contains(resNum.ToString()) ? EnumBetWinLose.中 : EnumBetWinLose.挂;
            }

            if (playType.Contains("念"))
            {
                int numA = Convert.ToInt32(Ai.GetTextLeft(playType, "念").Trim());
                int numB = Convert.ToInt32(Ai.GetTextRight(playType, "念").Trim());

                // 中
                if (resNum == numA)
                {
                    return EnumBetWinLose.中;
                }

                // 和
                if (resNum == numB)
                {
                    return EnumBetWinLose.和;
                }

                // 挂
                return EnumBetWinLose.挂;
            }

            if (playType.Contains("单"))
            {
                return resNum % 2 == 1 ? EnumBetWinLose.中 : EnumBetWinLose.挂;
            }

            if (playType.Contains("双"))
            {
                return resNum % 2 == 0 ? EnumBetWinLose.中 : EnumBetWinLose.挂;
            }

            if (playType.Contains("大"))
            {
                return resNum >= 3 ? EnumBetWinLose.中 : EnumBetWinLose.挂;
            }

            if (playType.Contains("小"))
            {
                return resNum <= 2 ? EnumBetWinLose.中 : EnumBetWinLose.挂;
            }

            if (playType.Contains("门"))
            {
                // 中
                string con = Ai.GetTextRight(playType, "门");
                if (con.Contains(resNum.ToString()))
                {
                    return EnumBetWinLose.中;
                }

                // 挂
                return EnumBetWinLose.挂;
            }

            if (playType.Contains("无"))
            {
                int numA = Convert.ToInt32(Ai.GetTextLeft(playType, "无").Trim());
                int numB = Convert.ToInt32(Ai.GetTextRight(playType, "无").Trim());

                // 中
                if (resNum == numA)
                {
                    return EnumBetWinLose.中;
                }

                // 挂
                if (resNum == numB)
                {
                    return EnumBetWinLose.挂;
                }

                // 和
                return EnumBetWinLose.和;
            }
        }
        catch (Exception ex)
        {
            // Debug.WriteLine(ex);
            await DbHelper.AddLog(EnumLogType.机器人, "判断项目中挂Error", ex.ToString());
        }

        return EnumBetWinLose.未开奖;
    }

    #endregion

    #region 获取会员详情信息

    /// <summary>
    /// 获取会员详情信息
    /// </summary>
    /// <param name="member"></param>
    /// <returns></returns>
    public static async Task<MemberInfo> GetMemberDetail(Member member)
    {
        try
        {
            // 取出此会员所有订单
            List<BetOrder> betOrderList = await DbHelper.FSql.Select<BetOrder>()
                .Where(a => a.Account.Equals(member.Account))
                .Where(a => a.BetOrderStatus == EnumBetOrderStatus.已受理 || a.BetOrderStatus == EnumBetOrderStatus.已结算)
                .ToListAsync();

            // 返回会员详情
            return new MemberInfo
            {
                账号 = member.Account,
                备注名 = member.备注名,
                积分 = Math.Round(member.Balance, 2),

                // 本期下注 = 所有已受理或已结算的订单的下注金额总和
                本期下注 = Math.Round(betOrderList
                    .Where(a => a.Issue == IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue)
                    .Where(a => a.BetOrderStatus == EnumBetOrderStatus.已受理 || a.BetOrderStatus == EnumBetOrderStatus.已结算) // 所有已受理或已结算的订单
                    .Sum(a => a.Money), 2),

                // 总流水 = 所有中或挂的订单的下注金额总和
                总流水 = Math.Round(betOrderList
                    .Where(a => a.WinLose == EnumBetWinLose.中 || a.WinLose == EnumBetWinLose.挂) // 所有中或挂的订单才能算流水
                    .Sum(a => a.Money), 2), // 所有中或挂的金额总和

                // 总盈亏 = 所有中奖的金额 + 所有回水的金额 - 所有订单的下注金额
                总盈亏 = Math.Round(betOrderList.Where(a => a.WinLose == EnumBetWinLose.中)
                                     .Sum(a => a.结算) // 所有中的金额
                                 - betOrderList.Where(a => a.WinLose == EnumBetWinLose.中 || a.WinLose == EnumBetWinLose.挂) // 所有中或挂的金额
                                     .Sum(a => a.Money), 2), // 减去所有订单的下注金额

                // 未回流水 = 所有已结算(中或挂)的订单且未回水的订单的下注金额总和
                未回流水 = Math.Round(betOrderList
                    .Where(a => a is { BetOrderStatus: EnumBetOrderStatus.已结算, BetOrderRebateStatus: EnumBetOrderRebateStatus.未回水 } && (a.WinLose == EnumBetWinLose.中 || a.WinLose == EnumBetWinLose.挂))
                    .Sum(a => a.Money), 2),

                // 已回流水 = 所有已结算(中或挂)的订单且已回水的订单的下注金额总和
                已回流水 = Math.Round(betOrderList
                    .Where(a => a is { BetOrderStatus: EnumBetOrderStatus.已结算, BetOrderRebateStatus: EnumBetOrderRebateStatus.已回水 })
                    .Sum(a => a.Money), 2),

                回水比例 = member.回水比例,

                // 已回金额 = 所有已回水的订单的回水金额总和
                已回金额 = Math.Round(betOrderList.Where(a => a.BetOrderRebateStatus == EnumBetOrderRebateStatus.已回水).Sum(a => a.回水金额), 2),

                总上分 = (decimal)Math.Round((double)await DbHelper.FSql.Select<AddMoney>()
                    .Where(a => a.Account.Equals(member.Account))
                    .Where(a => a.Status == EnumBalanceStatus.同意)
                    .SumAsync(a => a.Money), 2),

                总下分 = (decimal)Math.Round((double)await DbHelper.FSql.Select<SubMoney>()
                    .Where(a => a.Account.Equals(member.Account))
                    .Where(a => a.Status == EnumBalanceStatus.同意)
                    .SumAsync(a => a.Money), 2),

                // 真流水 = 所有真人订单中或挂的金额总和
                真流水 = Math.Round(betOrderList
                    .Where(a => a.OrderType == EnumOrderType.真人订单 && (a.WinLose == EnumBetWinLose.中 || a.WinLose == EnumBetWinLose.挂))
                    .Sum(a => a.Money), 2), // 真人订单中或挂的金额总和

                真盈亏 = Math.Round(betOrderList
                                     .Where(a => a is { OrderType: EnumOrderType.真人订单, WinLose: EnumBetWinLose.中 })
                                     .Sum(a => a.结算) // 真人订单中的金额
                                 - betOrderList
                                     .Where(a => a.OrderType == EnumOrderType.真人订单 && (a.WinLose == EnumBetWinLose.中 || a.WinLose == EnumBetWinLose.挂))
                                     .Sum(a => a.Money), 2), // 减去真人订单的下注金额

                // 假流水 = 所有假人订单中或挂的金额总和
                假流水 = Math.Round(betOrderList
                    .Where(a => a.OrderType == EnumOrderType.假人订单 && (a.WinLose == EnumBetWinLose.中 || a.WinLose == EnumBetWinLose.挂))
                    .Sum(a => a.Money), 2), // 假人订单中或挂的金额总和

                假盈亏 = Math.Round(betOrderList
                                     .Where(a => a is { OrderType: EnumOrderType.假人订单, WinLose: EnumBetWinLose.中 })
                                     .Sum(a => a.结算) // 假人订单中的金额
                                 - betOrderList
                                     .Where(a => a.OrderType == EnumOrderType.假人订单 && (a.WinLose == EnumBetWinLose.中 || a.WinLose == EnumBetWinLose.挂))
                                     .Sum(a => a.Money), 2) // 减去假人订单的下注金额
            };
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "获取会员详情出错", ex.ToString());
        }

        return new MemberInfo();
    }

    #endregion

    #region 获取会员已受理订单列表

    /// <summary>
    /// 获取会员已受理订单列表
    /// </summary>
    /// <param name="account"></param>
    /// <param name="issue"></param>
    /// <returns></returns>
    private static async Task<List<BetOrder>> GetBetOrderList(string account, string issue)
    {
        return await DbHelper.FSql.Select<BetOrder>()
            .Where(a => a.Account.Equals(account))
            .Where(a => a.Issue == issue)
            .Where(a => a.BetOrderStatus == EnumBetOrderStatus.已受理)
            .ToListAsync();
    }

    #endregion

    // ##################################

    #region 根据不同彩种提取不同的开奖号码

    /// <summary>
    /// 根据不同彩种提取不同的开奖号码
    /// </summary>
    /// <param name="kj"></param>
    /// <param name="betLottery"></param>
    /// <returns></returns>
    public static async Task<string> GetDrawNum(KaiJiang kj, EnumBetLottery betLottery)
    {
        string drawNumResult = "";
        try
        {
            if (betLottery == EnumBetLottery.台湾宾果1)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawNumResult = drawNum.Last();
            }
            else if (betLottery == EnumBetLottery.台湾宾果2)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawNumResult = drawNum[0] + "," + drawNum[^2] + "," + drawNum[^1];
            }
            else if (betLottery == EnumBetLottery.台湾宾果3)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                for (int i = 0; i < drawNum.Length - 1; i++)
                {
                    drawNumResult += drawNum[i] + ",";
                }

                drawNumResult = drawNumResult.TrimEnd(',');
            }
            else if (betLottery == EnumBetLottery.一六八飞艇前3)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawNumResult = drawNum[0] + "," + drawNum[1] + "," + drawNum[2];
            }
            else if (betLottery == EnumBetLottery.一六八飞艇中3)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawNumResult = drawNum[4] + "," + drawNum[5] + "," + drawNum[6];
            }
            else if (betLottery == EnumBetLottery.一六八飞艇后3)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawNumResult = drawNum[7] + "," + drawNum[8] + "," + drawNum[9];
            }
            else if (betLottery == EnumBetLottery.新一六八XL前)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawNumResult = drawNum[0] + "," + drawNum[1] + "," + drawNum[2] + "," + drawNum[3] + "," + drawNum[4];
            }
            else if (betLottery == EnumBetLottery.新一六八XL中)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawNumResult = drawNum[0] + "," + drawNum[1] + "," + drawNum[2] + "," + drawNum[5] + "," + drawNum[6];
            }
            else if (betLottery == EnumBetLottery.新一六八XL后)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawNumResult = drawNum[0] + "," + drawNum[1] + "," + drawNum[2] + "," + drawNum[7] + "," + drawNum[8];
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "GetDrawNumError", ex.ToString());
        }


        return drawNumResult;
    }

    #endregion

    #region 根据不同彩种计算不同的开奖结果

    /// <summary>
    /// 根据不同彩种计算不同的开奖结果
    /// </summary>
    /// <param name="kj"></param>
    /// <param name="betLottery"></param>
    /// <returns></returns>
    public static async Task<int> GetDrawResult(KaiJiang kj, EnumBetLottery betLottery)
    {
        int drawResult = 0;

        try
        {
            if (betLottery == EnumBetLottery.台湾宾果1)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawResult = int.Parse(drawNum[^1]);
                drawResult %= 4;
                drawResult = drawResult == 0 ? 4 : drawResult;
            }
            else if (betLottery == EnumBetLottery.台湾宾果2)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawResult = int.Parse(drawNum[0]) + int.Parse(drawNum[^2]) + int.Parse(drawNum[^1]);
                drawResult %= 4;
                drawResult = drawResult == 0 ? 4 : drawResult;
            }
            else if (betLottery == EnumBetLottery.台湾宾果3)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                for (int i = 0; i < drawNum.Length - 1; i++)
                {
                    drawResult += int.Parse(drawNum[i]);
                }

                drawResult %= 4;
                drawResult = drawResult == 0 ? 4 : drawResult;
            }
            else if (betLottery == EnumBetLottery.一六八飞艇前3)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawResult = int.Parse(drawNum[0]) + int.Parse(drawNum[1]) + int.Parse(drawNum[2]);
                drawResult %= 4;
                drawResult = drawResult == 0 ? 4 : drawResult;
            }
            else if (betLottery == EnumBetLottery.一六八飞艇中3)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawResult = int.Parse(drawNum[4]) + int.Parse(drawNum[5]) + int.Parse(drawNum[6]);
                drawResult %= 4;
                drawResult = drawResult == 0 ? 4 : drawResult;
            }
            else if (betLottery == EnumBetLottery.一六八飞艇后3)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawResult = int.Parse(drawNum[7]) + int.Parse(drawNum[8]) + int.Parse(drawNum[9]);
                drawResult %= 4;
                drawResult = drawResult == 0 ? 4 : drawResult;
            }
            else if (betLottery == EnumBetLottery.新一六八XL前)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawResult = int.Parse(drawNum[0]) + int.Parse(drawNum[1]) + int.Parse(drawNum[2]) + int.Parse(drawNum[3]) + int.Parse(drawNum[4]);
                drawResult %= 4;
                drawResult = drawResult == 0 ? 4 : drawResult;
            }
            else if (betLottery == EnumBetLottery.新一六八XL中)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawResult = int.Parse(drawNum[0]) + int.Parse(drawNum[1]) + int.Parse(drawNum[2]) + int.Parse(drawNum[5]) + int.Parse(drawNum[6]);
                drawResult %= 4;
                drawResult = drawResult == 0 ? 4 : drawResult;
            }
            else if (betLottery == EnumBetLottery.新一六八XL后)
            {
                string[] drawNum = Ai.Split(kj.DrawNum, ",");
                drawResult = int.Parse(drawNum[0]) + int.Parse(drawNum[1]) + int.Parse(drawNum[2]) + int.Parse(drawNum[7]) + int.Parse(drawNum[8]);
                drawResult %= 4;
                drawResult = drawResult == 0 ? 4 : drawResult;
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "GetDrawResultError", ex.ToString());
        }

        return drawResult;
    }

    #endregion
}