using System.Net;
using System.Reflection;
using AiHelper;
using AutoUpdaterDotNET;
using Flurl.Http;
using Robot.Config;
using Robot.Enum;
using Robot.Helper;

namespace Robot
{
    internal static class Program
    {
        /// <summary>
        /// 应用程序的主入口点
        /// </summary>
        [STAThread]
        static void Main()
        {
            // 初始化应用程序配置，如设置高DPI设置或默认字体等
            // 详细信息请参考：https://aka.ms/applicationconfiguration
            ApplicationConfiguration.Initialize();

            // 载入配置参数
            Setting.Current.Load();

            // 确定游戏
            CommonHelper.Lottery = (EnumLottery)Setting.Current.AppType;

            // 检查是否已经运行,防止多开
            if (!SingleInstance.IsContinue())
            {
                MessageBox.Show(@"KingRobot已经在运行，请勿重复打开！");
                return;
            }

            // 设置Expect100Continue为false
            ServicePointManager.Expect100Continue = false;

            // 设置SecurityProtocol为支持的TLS版本,设置为SecurityProtocolType.SystemDefault可以让系统自动选择最佳的安全协议
            // ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3 | SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;
            // ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;
            ServicePointManager.SecurityProtocol = SecurityProtocolType.SystemDefault;

            // 取执行文件版本号
            string? version = Assembly.GetExecutingAssembly().GetName().Version?.ToString().Replace(".", "");

            // 根据不同的彩种设置更新地址
            string updateUrl = "";
            switch (CommonHelper.Lottery)
            {
                case EnumLottery.台湾宾果:
                    updateUrl = $"{CommonHelper.LicenseServer}/AutoUpdater/RobotTwbg/AutoUpdater.xml";
                    break;
                case EnumLottery.一六八飞艇:
                    updateUrl = $"{CommonHelper.LicenseServer}/AutoUpdater/Robot168ft/AutoUpdater.xml";
                    break;
                case EnumLottery.新一六八XL:
                    updateUrl = $"{CommonHelper.LicenseServer}/AutoUpdater/RobotNew168XL/AutoUpdater.xml";
                    break;
            }

            // 获取更新信息
            string responseContent = updateUrl.WithTimeout(TimeSpan.FromSeconds(3)).GetAsync().ReceiveString().Result;

            // 先自行判断是否有新版本
            if (!string.IsNullOrEmpty(version) && !string.IsNullOrWhiteSpace(responseContent) && responseContent.Contains("<version>"))
            {
                string newVersion = Ai.GetTextMiddle(responseContent, "<version>", "</version>").Trim().Replace(".", "");
                if (Convert.ToInt32(newVersion) > Convert.ToInt32(version))
                {
                    // 异步检查更新
                    AutoUpdater.Start(updateUrl);
                    int sleepTime = 60 * 60 * 24 * 1000;
                    Thread.Sleep(sleepTime);
                }
            }

            // 载入参数设置窗口
            FormIndex formIndex = new FormIndex();
            formIndex.ShowDialog();

            // 载入主窗体
            if (formIndex.DialogResult == DialogResult.OK && RegisterHelper.MyRegisterInfo.ExpireTime > CommonHelper.DateTimeNowInternet)
            {
                Application.Run(new FormMain());
            }
        }
    }
}