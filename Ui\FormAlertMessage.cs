﻿namespace Robot.Ui
{
    public enum AlertType
    {
        Info,
        Success,
        Warning,
        Error
    }

    public enum AlertFormAction
    {
        Start,
        Wait,
        Close
    }

    public partial class FormAlertMessage : Form
    {
        private int _x, _y;
        private AlertFormAction Action { get; set; }

        public FormAlertMessage()
        {
            InitializeComponent();
            Opacity = 0.0; // 将初始透明度移至构造函数
        }

        private void timer_Close_Tick(object sender, EventArgs e)
        {
            switch (Action)
            {
                case AlertFormAction.Start:
                    HandleStartAction();
                    break;
                case AlertFormAction.Wait:
                    HandleWaitAction();
                    break;
                case AlertFormAction.Close:
                    HandleCloseAction();
                    break;
            }
        }

        public void Show(string message, AlertType type)
        {
            SetInitialPosition();
            labelContent.Text = message;
            SetAlertTheme(type);
            Show();

            Action = AlertFormAction.Start;
            timer_Close.Start();
        }

        private void SetAlertTheme(AlertType type)
        {
            BackColor = type switch
            {
                AlertType.Info => Color.RoyalBlue,
                AlertType.Success => Color.SeaGreen,
                AlertType.Warning => Color.DarkOrange,
                AlertType.Error => Color.DarkRed,
                _ => BackColor,
            };
        }

        private void SetInitialPosition()
        {
            StartPosition = FormStartPosition.Manual;
            for (int i = 1; i < 10; i++)
            {
                if (Application.OpenForms["alert" + i] == null)
                {
                    Name = "alert" + i;
                    _x = Screen.PrimaryScreen!.WorkingArea.Width - Width;
                    _y = Screen.PrimaryScreen.WorkingArea.Height - Height * i - 5 * i;
                    Location = new Point(_x, _y);
                    break;
                }
            }
        }

        private void HandleStartAction()
        {
            timer_Close.Interval = 50;
            AdjustOpacity(0.1);
            if (Opacity >= 1.0)
            {
                Action = AlertFormAction.Wait;
                timer_Close.Interval = 6000; // 进入等待状态直接设置间隔
            }
        }

        private void HandleWaitAction()
        {
            Action = AlertFormAction.Close; // 直接进入关闭动作
        }

        private void HandleCloseAction()
        {
            timer_Close.Interval = 50;
            AdjustOpacity(-0.1);
            if (Opacity <= 0.0)
            {
                Close();
            }
        }

        private void AdjustOpacity(double change)
        {
            Opacity = Math.Clamp(Opacity + change, 0.0, 1.0); // 使用 Math.Clamp 限制 Opacity 的范围
        }
    }
}