﻿using System.Diagnostics;
using System.Globalization;
using AiHelper;
using Flurl.Http;
using FreeSql;
using Robot.ChatPlatform;
using Robot.Config;
using Robot.Enum;
using Robot.Helper;
using Robot.Models;
using Sunny.UI;

namespace Robot.Ui;

/// <summary>
/// UI事件
/// </summary>
public partial class FormMain
{
    #region 设置飞单状态

    /// <summary>
    /// 设置飞单状态
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void checkBox_IsFeiDan_CheckedChanged(object? sender, EventArgs e)
    {
        try
        {
            checkBox_IsFeiDan.ForeColor = checkBox_IsFeiDan.Checked ? Color.Red : Color.Black;
            Setting.Current.是否开启飞单 = checkBox_IsFeiDan.Checked;
            Setting.Current.Save();
            await DbHelper.AddLog(EnumLogType.机器人, "设置飞单状态", Setting.Current.是否开启飞单 ? "开启" : "关闭");
            this.ShowSuccessTip(Setting.Current.是否开启飞单 ? "已开启飞单" : "已关闭飞单");
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "checkBox_IsFeiDan_CheckedChangedError", ex.ToString());
        }
    }

    #endregion

    #region 设置对冲吃单状态

    /// <summary>
    /// 是否开启对冲吃单
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void checkBox_IsDuiChong_CheckedChanged(object? sender, EventArgs e)
    {
        try
        {
            checkBox_IsDuiChong.ForeColor = checkBox_IsDuiChong.Checked ? Color.Red : Color.Black;
            Setting.Current.是否对冲吃单 = checkBox_IsDuiChong.Checked;
            Setting.Current.Save();
            await DbHelper.AddLog(EnumLogType.机器人, "设置对冲吃单状态", $"{Setting.Current.是否对冲吃单}");
            this.ShowSuccessTip(Setting.Current.是否对冲吃单 ? "已开启对冲吃单" : "已关闭对冲吃单");
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "checkBox_IsDuiChong_CheckedChangedError", ex.ToString());
        }
    }

    #endregion

    #region 飞单失败自动退单

    /// <summary>
    /// 飞单失败自动退单
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void checkBox_CancelOrder_CheckedChanged(object sender, EventArgs e)
    {
        try
        {
            checkBox_CancelOrder.ForeColor = checkBox_CancelOrder.Checked ? Color.Red : Color.Black;
            Setting.Current.AutoCancelOrder = checkBox_CancelOrder.Checked;
            this.ShowSuccessTip(Setting.Current.AutoCancelOrder ? "已开启飞单失败自动退单" : "已关闭飞单失败自动退单");
            if (Setting.Current.AutoCancelOrder)
            {
                await DbHelper.AddLog(EnumLogType.机器人, "开启飞单失败自动退单", $"{Setting.Current.AutoCancelOrder}");
                UIMessageBox.ShowWarning2("开启自动退单可能会有一定的误判风险,请知悉!");
            }
            else
            {
                await DbHelper.AddLog(EnumLogType.机器人, "关闭飞单失败自动退单", $"{Setting.Current.AutoCancelOrder}");
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "checkBox_CancelOrder_CheckedChangedError", ex.ToString());
        }
    }

    #endregion

    #region 开启/停止服务

    /// <summary>
    /// 开启/停止服务
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void uiButton_Service_Click(object? sender, EventArgs e)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue))
            {
                await DbHelper.AddLog(EnumLogType.机器人, "拦截开始服务", "飞单平台未登录.");
                UIMessageBox.ShowMessageDialog("请先登录飞单平台后再开始考试!", "操作提醒:", false, UIStyle.Red, true);
                return;
            }

            try
            {
                if (uiButton_Service.Text.Contains("开始"))
                {
                    uiButton_Service.Enabled = false;

                    // 判断是否已经指定考试群
                    if (comboBox_WorkGroupId.SelectedIndex <= 0)
                    {
                        await DbHelper.AddLog(EnumLogType.机器人, "拦截开始服务", "未指定考试群");
                        UIMessageBox.ShowMessageDialog("请先指定考试群再开始考试!", "操作提醒:", false, UIStyle.Red, true);
                        uiButton_Service.Enabled = true;
                        return;
                    }

                    // 判断是否已经链接通讯App
                    if (!RobotHelper.GroupDic.Any())
                    {
                        await DbHelper.AddLog(EnumLogType.机器人, "拦截开始服务", "未指定考试群");
                        UIMessageBox.ShowMessageDialog("请先登录通讯App后再开始考试!", "操作提醒:", false, UIStyle.Red, true);
                        return;
                    }

                    // 设置机器人参数
                    RobotHelper.WorkGroupId = Ai.GetTextMiddle(comboBox_WorkGroupId.Text.Trim(), Ai.中括号左, Ai.中括号右);
                    SoundHelper.MediaStartService.Play();

                    // 判断是否发送开奖数据图
                    if (Setting.Current.是否发送开奖图)
                    {
                        await RobotHelper.发送开奖图Handler();
                    }

                    // 判断是否发送开奖路子图
                    if (Setting.Current.是否发送路子图)
                    {
                        await Task.Delay(500);
                        await RobotHelper.发送路子图Handler();
                    }

                    // // 发送数据注意提醒
                    // if (CommonHelper.Lottery.Equals(EnumLottery.台湾宾果2) && DateTime.Now < new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 7, 5, 0))
                    // {
                    //     await ChatHelper.SendGroupMessage($"【请注意】以上是昨天最后一期的开奖结果！");
                    // }

                    // 发送开始服务提示
                    await Task.Delay(1000);
                    await ChatHelper.SendGroupMessage("【开始服务】" + "\r" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    Thread.Sleep(1000);

                    // 发送当前时间
                    await RobotHelper.查询时间指令Handler(null);
                    await ChatHelper.SendGroupMessage("【请等待开始答题】");

                    // 发送开盘提醒
                    if (RobotHelper.OpenTipsList.Contains(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue))
                    {
                        await Task.Delay(500);
                        await RobotHelper.发送开盘提醒(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue);
                    }

                    // 记录进数据库
                    await DbHelper.AddLog(EnumLogType.机器人, "启动服务", "当前考试群为:" + Ai.中括号左 + RobotHelper.WorkGroupId + Ai.中括号右);
                    comboBox_WorkGroupId.Enabled = false;
                    uiButton_Service.Text = @"停止服务";

                    // 开始服务
                    CommonHelper.ServiceParamDic[CommonHelper.IsStartService] = true;
                    uiButton_Service.Enabled = true;
                    CommonHelper.StartServiceTime = DateTime.Now;
                }
                else
                {
                    // 发送停止服务提示
                    await ChatHelper.SendGroupMessage("【停止服务】" + "\r" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

                    // 重置服务参数
                    CommonHelper.StartServiceTime = new DateTime(1970, 1, 1, 8, 0, 0);
                    CommonHelper.ServiceParamDic[CommonHelper.IsStartService] = false;
                    SoundHelper.MediaStopService.Play();
                    comboBox_WorkGroupId.Enabled = true;
                    uiButton_Service.Text = @"开始服务";
                }
            }
            catch (Exception ex)
            {
                await DbHelper.AddLog(EnumLogType.机器人, $"{Ai.中括号左}开始服务{Ai.中括号右}", ex.ToString());
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "button_Service_ClickError", ex.ToString());
        }
    }

    #endregion

    #region 飞单失败手动补投

    /// <summary>
    /// 飞单失败手动补投
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void uiButton_HandToBet_Click(object sender, EventArgs e)
    {
        try
        {
            uiButton_HandToBet.Enabled = false;
            await DbHelper.AddLog(EnumLogType.机器人, "手动补投", "开始手动补投.");
            await HandToBet();
            await BetHelper.CheckBetResult();
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "button_HandToBet_ClickError", ex.ToString());
        }
        finally
        {
            uiButton_HandToBet.Enabled = true;
        }
    }

    /// <summary>
    /// HandToBet
    /// </summary>
    private async Task HandToBet()
    {
        try
        {
            // 先将本期所有失败的记录标记为未知
            await DbHelper.FSql.Update<BetResult>()
                .Set(a => a.Result, EnumBetResult.未知)
                .Where(a => a.Issue == Setting.Current.LastBetIssue)
                .Where(a => a.Result == EnumBetResult.失败)
                .ExecuteAffrowsAsync();

            await DbHelper.FSql.Update<HuiZong>()
                .Set(a => a.BetResult, EnumBetResult.未知)
                .Where(a => a.Issue == Setting.Current.LastBetIssue)
                .Where(a => a.BetResult == EnumBetResult.失败)
                .ExecuteAffrowsAsync();

            // 从汇总表提取出最后一期所有待飞单数据
            var toBetList = await DbHelper.FSql.Select<HuiZong>()
                .Where(a => a.Issue == Setting.Current.LastBetIssue)
                .Where(a => a.ToBetBalance > 0)
                .Where(a => a.BetResult == EnumBetResult.未知)
                .ToListAsync(a => new
                {
                    a.Id,
                    a.BetLottery,
                    a.Issue,
                    a.Content,
                    a.ToBetBalance
                });

            // 过滤游戏种类
            int selectIndex = 0;
            Invoke(() => { selectIndex = comboBox_BetLottery.SelectedIndex; });
            if (selectIndex.Equals(1))
            {
                toBetList = toBetList.Where(a => a.BetLottery.Equals(EnumBetLottery.台湾宾果1)).ToList();
            }
            else if (selectIndex.Equals(2))
            {
                toBetList = toBetList.Where(a => a.BetLottery.Equals(EnumBetLottery.台湾宾果2)).ToList();
            }
            else if (selectIndex.Equals(3))
            {
                toBetList = toBetList.Where(a => a.BetLottery.Equals(EnumBetLottery.台湾宾果3)).ToList();
            }
            else if (selectIndex.Equals(4))
            {
                toBetList = toBetList.Where(a => a.BetLottery.Equals(EnumBetLottery.一六八飞艇前3)).ToList();
            }
            else if (selectIndex.Equals(5))
            {
                toBetList = toBetList.Where(a => a.BetLottery.Equals(EnumBetLottery.一六八飞艇中3)).ToList();
            }
            else if (selectIndex.Equals(6))
            {
                toBetList = toBetList.Where(a => a.BetLottery.Equals(EnumBetLottery.一六八飞艇后3)).ToList();
            }

            if (!toBetList.Any())
            {
                Invoke(() => { UIMessageBox.ShowWarning(@"该彩种没有可投注数据."); });
                return;
            }

            // 提取游戏种类
            HashSet<EnumBetLottery> betLotteryList = [.. toBetList.Select(betOrder => betOrder.BetLottery)];
            betLotteryList = [.. betLotteryList.OrderBy(enumBetLottery => (int)enumBetLottery)];
            await DbHelper.AddLog(EnumLogType.平台, "飞单游戏", string.Join(",", betLotteryList));

            // 遍历游戏种类
            foreach (EnumBetLottery betLottery in betLotteryList)
            {
                // 过滤游戏种类
                if (toBetList.All(a => a.BetLottery != betLottery))
                {
                    continue;
                }

                // 拼接飞单数据,用于显示到右下角日志
                var postBetList = toBetList.Where(a => a.BetLottery == betLottery).ToList();
                string betContent = Ai.中括号左 + toBetList[0].Issue + Ai.中括号右 + string.Join("", postBetList.Select(bet => Ai.中括号左 + bet.Content + "-" + bet.ToBetBalance + Ai.中括号右));

                // 记录日志
                await DbHelper.AddLog(EnumLogType.平台, "飞单内容", $"{Ai.中括号左}{betLottery}{Ai.中括号右}" + betContent);

                // 取出BetResult
                BetResult betResult = await DbHelper.FSql.Select<BetResult>()
                    .Where(a => a.BetLottery == betLottery)
                    .Where(a => a.Issue == Setting.Current.LastBetIssue)
                    .FirstAsync();

                // 发送飞单数据,不设置超时时间
                string url = $"{Setting.Current.PlatformHost}/ToBet/";
                string responseContent;
                try
                {
                    responseContent = await url
                        .PostJsonAsync(postBetList)
                        .ReceiveString();
                }
                catch (Exception ex)
                {
                    responseContent = ex.ToString();
                    await DbHelper.AddLog(EnumLogType.平台, "AutoBet", ex.ToString());
                }

                await DbHelper.AddLog(EnumLogType.平台, "飞单反馈", $@"{responseContent}");

                // 判断是否投注成功
                bool isSuccess = false;
                if (responseContent.Contains("投注成功"))
                {
                    isSuccess = true;
                }
                else if (responseContent.Contains("投注成功2"))
                {
                    isSuccess = true;
                }

                // 飞单成功
                if (isSuccess)
                {
                    // 更新汇总表
                    await DbHelper.FSql.Update<HuiZong>()
                        .Set(a => a.BetResult, EnumBetResult.成功)
                        .Where(a => a.Issue == IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue)
                        .ExecuteAffrowsAsync();

                    // 更新游戏种类结果
                    await DbHelper.FSql.Update<BetResult>()
                        .Set(a => a.Result, EnumBetResult.成功)
                        .Where(a => a.BetLottery == betResult.BetLottery)
                        .Where(a => a.Issue == betResult.Issue)
                        .ExecuteAffrowsAsync();

                    // 记录日志
                    await DbHelper.AddLog(EnumLogType.平台, "飞单成功", $@"{Ai.中括号左}{betLottery}@{IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue}{Ai.中括号右}飞单成功.");

                    // 弹出提示框
                    Invoke(() =>
                    {
                        FormAlertMessage frm = new FormAlertMessage();
                        frm.Show($@"{Ai.中括号左}{betLottery}@{IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue}{Ai.中括号右}飞单成功.", AlertType.Success);
                    });

                    // 播放成功提示音,然后继续下一轮循环
                    SoundHelper.MediaBetSuccess.Play();
                }
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "ToBetError", ex.ToString());
        }
    }

    #endregion

    #region 飞单失败一键退回

    /// <summary>
    /// 飞单失败一键退回
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void uiButton_CancelBetOrder_Click(object sender, EventArgs e)
    {
        try
        {
            // 先判断是否开启自动退单
            if (Setting.Current.AutoCancelOrder)
            {
                UIMessageBox.ShowWarning(@"你已开启自动退单,请等待系统自动操作!", true);
                return;
            }

            await DbHelper.AddLog(EnumLogType.机器人, "手动退回", "开始手动退回.");

            // 取出最后已受理状态的投注记录
            List<BetOrder> toBetList = await DbHelper.FSql.Select<BetOrder>()
                .Where(a => a.BetOrderStatus == EnumBetOrderStatus.已受理)
                .OrderBy(a => a.Id)
                .ToListAsync();

            // 根据选择过滤数据
            int selectIndex = 0;
            Invoke(() => { selectIndex = comboBox_BetLottery.SelectedIndex; });
            if (selectIndex.Equals(1))
            {
                toBetList = toBetList.Where(a => a.BetLottery.Equals(EnumBetLottery.台湾宾果1)).ToList();
            }
            else if (selectIndex.Equals(2))
            {
                toBetList = toBetList.Where(a => a.BetLottery.Equals(EnumBetLottery.台湾宾果2)).ToList();
            }
            else if (selectIndex.Equals(3))
            {
                toBetList = toBetList.Where(a => a.BetLottery.Equals(EnumBetLottery.台湾宾果3)).ToList();
            }
            else if (selectIndex.Equals(4))
            {
                toBetList = toBetList.Where(a => a.BetLottery.Equals(EnumBetLottery.一六八飞艇前3)).ToList();
            }
            else if (selectIndex.Equals(5))
            {
                toBetList = toBetList.Where(a => a.BetLottery.Equals(EnumBetLottery.一六八飞艇中3)).ToList();
            }
            else if (selectIndex.Equals(6))
            {
                toBetList = toBetList.Where(a => a.BetLottery.Equals(EnumBetLottery.一六八飞艇后3)).ToList();
            }

            // 弹出确认对话框
            if (!toBetList.Any())
            {
                UIMessageBox.ShowMessageDialog("当前没有可被退回的数据.", @"操作提醒", false, UIStyle.Orange, true);
                return;
            }

            // 获取最后一条数据,并且根据最后一条数据过滤数据
            BetOrder lastBetOrder = Enumerable.Last(toBetList);
            toBetList = toBetList.Where(a => a.Issue.Equals(lastBetOrder.Issue)).ToList();

            // 弹出确认对话框
            if (UIMessageBox.ShowMessageDialog(@"确定要撤销指定期号的投注数据吗?该操作非常危险且不可逆,撤销后将无法恢复,请谨慎操作!确定撤销请按确定按钮,取消请按取消按钮？", @"确认操作", true, UIStyle.Red))
            {
                BetHelper.CancelBetData(toBetList);
            }

            // 弹出确认对话框
            // DialogResult result = MessageBox.Show(@"确定要撤销指定期号的投注数据吗?该操作非常危险且不可逆,撤销后将无法恢复,请谨慎操作!确定撤销请按确定按钮,取消请按取消按钮？", @"确认操作", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            // if (result == DialogResult.Yes)
            // {
            //     await Task.Run(() => CancelBetData(toBetList));
            // }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "CancelBetOrderError", ex.ToString());
        }
    }

    #endregion

    #region 卡奖时手动开盘

    /// <summary>
    /// 卡奖时手动开盘
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void uiButton_HandToOpen_Click(object sender, EventArgs e)
    {
        try
        {
            await DbHelper.AddLog(EnumLogType.机器人, "操作手动开盘", "操作手动开盘.");

            // 先判断是否为卡奖时段
            if (!RobotHelper.OpenTipsList.Contains(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue))
            {
                RobotHelper.OpenTipsList.Add(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue);
                await RobotHelper.发送开盘提醒(IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue);
                await DbHelper.AddLog(EnumLogType.机器人, Ai.中括号左 + "开始接受答题" + Ai.中括号右, string.Concat(Ai.中括号左, IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue, Ai.中括号右, $"期开始接受答题\r{Ai.中括号左}本期为卡奖时段管理员手动开始{Ai.中括号右}"));

                // 发送开盘提示音
                SoundHelper.MediaOpenDraw.Play();
                UIMessageBox.ShowSuccess($@"{Ai.中括号左}{IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue}{Ai.中括号右}期已手动开盘,请留意群内消息.");
            }
            else
            {
                UIMessageBox.ShowWarning($@"{Ai.中括号左}{IssueTimeHelper.IssueTimeNowDic[CommonHelper.Lottery].Issue}{Ai.中括号右}期已在开盘状态,无需再次手动开盘.");
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "uiButton_HandToOpen_ClickError", ex.ToString());
        }
    }

    #endregion

    #region 菜单栏切换事件

    /// <summary>
    /// 菜单栏切换事件
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void tabControl_Main_SelectedIndexChanged(object sender, EventArgs e)
    {
        try
        {
            if (tabControl_Main.SelectedTab == tabPage_Home)
            {
                await DbHelper.AddLog(EnumLogType.机器人, "操作记录", "打开主页");
            }
            else if (tabControl_Main.SelectedTab == tabPage_BetOrderReport)
            {
                await DbHelper.AddLog(EnumLogType.机器人, "操作记录", "打开订单记录");
                // 添加会员下拉框
                await AddMemberToComboBox(comboBox_SelectMemberForBetOrder);

                // 添加期号下拉框
                List<string> tmpIssueList = DbHelper.FSql.Select<BetOrder>().ToList(a => a.Issue);
                List<string> issueList = tmpIssueList.Distinct().OrderBy(a => a).ToList();
                comboBox_SelectIssueForBetOrder.Items.Clear();
                comboBox_SelectIssueForBetOrder.Items.Add("全部");
                foreach (string issue in issueList)
                {
                    comboBox_SelectIssueForBetOrder.Items.Add(issue);
                }
            }
            else if (tabControl_Main.SelectedTab == tabPage_BetTotalReport)
            {
                await DbHelper.AddLog(EnumLogType.机器人, "操作记录", "打开飞单汇总");
                // 添加期号下拉框
                List<string> issueList = DbHelper.FSql.Select<HuiZong>().ToList(t => t.Issue);
                issueList.Reverse();
                issueList = issueList.Distinct().ToList();
                comboBox_SelectIssueForHuiZong.Items.Clear();
                comboBox_SelectIssueForHuiZong.Items.Add("全部");
                comboBox_SelectIssueForHuiZong.Items.Add("成功");
                comboBox_SelectIssueForHuiZong.Items.Add("失败");
                foreach (string issue in issueList)
                {
                    comboBox_SelectIssueForHuiZong.Items.Add(issue);
                }

                comboBox_SelectIssueForHuiZong.SelectedIndex = 0;
            }
            else if (tabControl_Main.SelectedTab == tabPage_AddMoneyReport)
            {
                await DbHelper.AddLog(EnumLogType.机器人, "操作记录", "打开上分记录");
                await AddMemberToComboBox(comboBox_SelectObjForAddMoneyReport);
            }
            else if (tabControl_Main.SelectedTab == tabPage_SubMoneyReport)
            {
                await DbHelper.AddLog(EnumLogType.机器人, "操作记录", "打开下分记录");
                await AddMemberToComboBox(comboBox_SelectObjForSubMoneyReport);
            }
            else if (tabControl_Main.SelectedTab == tabPage_FinanceReport)
            {
                await DbHelper.AddLog(EnumLogType.机器人, "操作记录", "打开财务记录");
                await AddMemberToComboBox(comboBox_SelectObjForFinanceReport);
            }
            else if (tabControl_Main.SelectedTab == tabPage_RecMsg)
            {
                await DbHelper.AddLog(EnumLogType.机器人, "操作记录", "打开消息记录");
                await AddMemberToComboBox(comboBox_SelectMemberForShowRecMsg);
            }
            else if (tabControl_Main.SelectedTab == tabPage_Setting)
            {
                await DbHelper.AddLog(EnumLogType.机器人, "操作记录", "打开系统设置");
                await Task.Run(LoadOdds);
            }
            else if (tabControl_Main.SelectedTab == tabPage_Log)
            {
                await DbHelper.AddLog(EnumLogType.机器人, "操作记录", "打开日志页面");
                await Task.Run(ShowLog);
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "tabControl_Main_SelectedIndexChangedError", ex.ToString());
        }
    }

    private async Task AddMemberToComboBox(ComboBox comboBox)
    {
        try
        {
            List<Member> memberList = await DbHelper.FSql.Select<Member>().ToListAsync();
            comboBox.Items.Clear();
            comboBox.Items.Add("全部");
            foreach (Member member in memberList)
            {
                comboBox.Items.Add($"{Ai.中括号左}{member.Account}{Ai.中括号右}{member.昵称}");
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "AddMemberToComboBoxError", ex.ToString());
        }
    }

    #endregion

    #region 系统设置

    /// <summary>
    /// 载入Odds
    /// </summary>
    private void LoadOdds()
    {
        Invoke(async () =>
        {
            try
            {
                List<Odds> list = DbHelper.FSql.Select<Odds>().ToListAsync().Result;
                DataGridView dgv = dataGridView_Odds;
                dgv.DataSource = list;

                if (dgv.Rows.Count > 0)
                {
                    // 设置列宽
                    dgv.Columns[^1].Width = 80;
                    dgv.Columns[^2].Width = 80;
                    dgv.Columns[^3].Width = 80;
                    dgv.Columns[^4].Width = 60;
                    dgv.Columns[^5].Width = 60;

                    // 设置列对齐方式
                    dgv.Columns[^1].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns[^2].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns[^3].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns[^4].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    dgv.Columns[^5].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

                    // 取消选中任何单元格
                    dgv.ClearSelection();
                }
            }
            catch (Exception ex)
            {
                await DbHelper.AddLog(EnumLogType.机器人, "LoadOddsError", ex.ToString());
            }
        });
    }

    /// <summary>
    /// 保存Odds
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void button_SaveOdds_Click(object? sender, EventArgs e)
    {
        try
        {
            await DbHelper.AddLog(EnumLogType.机器人, "操作记录", "保存赔率");
            DataGridView dgv = dataGridView_Odds;
            foreach (DataGridViewRow dr in dgv.Rows)
            {
                string con = dr.Cells[0]?.Value?.ToString()?.Trim()!;

                // 取出各项值
                int minStake = Convert.ToInt32(dr.Cells[2].Value);
                int maxStake = Convert.ToInt32(dr.Cells[3].Value);
                int totalStake = Convert.ToInt32(dr.Cells[4].Value);

                // 更新数据库
                await DbHelper.FSql.Update<Odds>()
                    .Set(a => a.最低限投, minStake)
                    .Set(a => a.最高限投, maxStake)
                    .Set(a => a.总额限投, totalStake)
                    .Where(a => a.项目 == con)
                    .ExecuteAffrowsAsync();
            }

            await Task.Run(LoadOdds);
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "button_SaveOdds_ClickError", ex.ToString());
        }
    }

    /// <summary>
    /// 设置封盘时间
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void comboBox_CloseTime_SelectedIndexChanged(object? sender, EventArgs e)
    {
        try
        {
            Setting.Current.封盘时间 = Convert.ToInt32(comboBox_CloseTime.Text.Trim());
            Setting.Current.Save();
            await DbHelper.AddLog(EnumLogType.机器人, "设置封盘时间", $"{Setting.Current.封盘时间}");
            // this.ShowSuccessTip($@"封盘时间已设置为{Ai.中括号左}{Setting.Current.封盘时间}{Ai.中括号右}秒");
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "comboBox_CloseTime_SelectedIndexChangedError", ex.ToString());
        }
    }

    /// <summary>
    /// 保存回水比例
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void button_SaveReturnCommissionPercent_Click(object? sender, EventArgs e)
    {
        try
        {
            // 取出输入值
            decimal percent = decimal.Parse(textBox_ReturnCommissionPercent.Text.Trim(), CultureInfo.InvariantCulture);
            if (percent < 0)
            {
                await Task.Run(() => { MessageBox.Show(@"回水比例必须大于或等于0."); });
                return;
            }

            // 比较输入值
            if (percent.Equals(Setting.Current.回水比例))
            {
                await Task.Run(() => { MessageBox.Show(@"输入的值与原有的值相同."); });
                return;
            }

            // 更新设置
            decimal oldReturnCommissionPercent = Setting.Current.回水比例;
            Setting.Current.回水比例 = Convert.ToDecimal(textBox_ReturnCommissionPercent.Text.Trim());
            Setting.Current.Save();

            // 更新数据库
            await DbHelper.FSql.Update<Member>()
                .Set(a => a.回水比例, Setting.Current.回水比例)
                .Where(a => a.回水比例.Equals(oldReturnCommissionPercent))
                .ExecuteAffrowsAsync();

            CommonHelper.NeedToRefreshMemberInfo = true;
            await DbHelper.AddLog(EnumLogType.机器人, "设置回水比例", Setting.Current.回水比例.ToString(CultureInfo.InvariantCulture));
            await Task.Run(() => { MessageBox.Show(@"设置回水比例成功."); });
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "SaveReturnCommissionPercentError", ex.ToString());
        }
    }

    /// <summary>
    /// 一键返水
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void button_OneKeyRebate_Click(object? sender, EventArgs e)
    {
        Task.Run(FinanceHelper.OneKeyRebate);
    }

    /// <summary>
    /// 7行路子图
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void checkBox_7Rows_CheckedChanged(object? sender, EventArgs e)
    {
        try
        {
            Setting.Current.SendImageRows7 = checkBox_7Rows.Checked;
            Setting.Current.Save();
            checkBox_7Rows.ForeColor = checkBox_7Rows.Checked ? Color.Red : Color.Black;
            await DbHelper.AddLog(EnumLogType.机器人, "设置发送路子图7行", Setting.Current.SendImageRows7 ? "开启" : "关闭");
            // this.ShowSuccessTip(Setting.Current.SendImageRows7 ? "已开启7行路子图" : "已关闭7行路子图");
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "checkBox_7Rows_CheckedChangedError", ex.ToString());
        }
    }

    /// <summary>
    ///  6行路子图
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void checkBox_6Rows_CheckedChanged(object? sender, EventArgs e)
    {
        try
        {
            Setting.Current.SendImageRows6 = checkBox_6Rows.Checked;
            Setting.Current.Save();
            checkBox_6Rows.ForeColor = checkBox_6Rows.Checked ? Color.Red : Color.Black;
            await DbHelper.AddLog(EnumLogType.机器人, "设置发送路子图6行", Setting.Current.SendImageRows6 ? "开启" : "关闭");
            // this.ShowSuccessTip(Setting.Current.SendImageRows6 ? "已开启6行路子图" : "已关闭6行路子图");
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "checkBox_6Rows_CheckedChangedError", ex.ToString());
        }
    }

    /// <summary>
    /// 摊路图模式1
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void radioButton_ImgType1_CheckedChanged(object sender, EventArgs e)
    {
        Setting.Current.ImgType = 1;
        Setting.Current.Save();
    }

    /// <summary>
    /// 摊路图模式2
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void radioButton_ImgType2_CheckedChanged(object sender, EventArgs e)
    {
        Setting.Current.ImgType = 2;
        Setting.Current.Save();
    }

    /// <summary>
    /// 自动回水
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void checkBox_AutoHuiShui_CheckedChanged(object? sender, EventArgs e)
    {
        try
        {
            checkBox_AutoHuiShui.ForeColor = checkBox_AutoHuiShui.Checked ? Color.Red : Color.Black;
            Setting.Current.自动回水 = checkBox_AutoHuiShui.Checked;
            Setting.Current.Save();
            await DbHelper.AddLog(EnumLogType.机器人, "设置自动回水状态", $"{Setting.Current.自动回水}");
            this.ShowSuccessTip(Setting.Current.自动回水 ? "已开启自动回水" : "已关闭自动回水");
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "checkBox_AutoHuiShui_CheckedChangedError", ex.ToString());
        }
    }

    /// <summary>
    /// 假人自动上分
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void checkBox_JiaRenAutoAddMoney_CheckedChanged(object? sender, EventArgs e)
    {
        try
        {
            checkBox_JiaRenAutoAddMoney.ForeColor = checkBox_JiaRenAutoAddMoney.Checked ? Color.Red : Color.Black;
            Setting.Current.假人自动上分 = checkBox_JiaRenAutoAddMoney.Checked;
            Setting.Current.Save();
            await DbHelper.AddLog(EnumLogType.机器人, "设置假人自动上分状态", $"{Setting.Current.假人自动上分}");
            this.ShowSuccessTip(Setting.Current.假人自动上分 ? "已开启假人自动上分" : "已关闭假人自动上分");
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "checkBox_JiaRenAutoAddMoney_CheckedChangedError", ex.ToString());
        }
    }


    /// <summary>
    /// 撤销指定期号投注数据
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void button_CancelBetData_Click(object? sender, EventArgs e)
    {
        try
        {
            string issue = textBox_CancelIssue.Text.Trim();
            if (string.IsNullOrWhiteSpace(issue))
            {
                await Task.Run(() => { MessageBox.Show(@"请输入需要撤销的期号.", @"操作提醒"); });
                return;
            }

            // 弹出确认对话框
            DialogResult result = MessageBox.Show(@"确定要撤销指定期号的投注数据吗?该操作非常危险且不可逆,撤销后将无法恢复,请谨慎操作!确定撤销请按确定按钮,取消请按取消按钮？", @"确认操作", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {
                List<BetOrder> toBetOrderList = await DbHelper.FSql.Select<BetOrder>()
                    .Where(a => a.Issue.Equals(issue))
                    .ToListAsync();
                BetHelper.CancelBetData(toBetOrderList);
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "CancelBetDataError", ex.ToString());
        }
    }


    /// <summary>
    /// 保留会员备注名
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void checkBox_SaveMemberRemarkName_CheckedChanged(object? sender, EventArgs e)
    {
        checkBox_SaveMemberBaseInfo.ForeColor = checkBox_SaveMemberBaseInfo.Checked ? Color.Red : Color.Black;
    }

    /// <summary>
    /// 保存会员余额
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void checkBox_SaveMemberBalance_CheckedChanged(object? sender, EventArgs e)
    {
        checkBox_SaveMemberBalance.ForeColor = checkBox_SaveMemberBalance.Checked ? Color.Red : Color.Black;
    }

    /// <summary>
    /// 清空数据按钮点击事件
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void button_Clear_Click(object? sender, EventArgs e)
    {
        try
        {
            // 弹出确认对话框
            DialogResult result = MessageBox.Show(@"删除数据无法恢复，请再次确认真的要清空数据吗?", @"确认操作", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

            // 如果用户点击了“是”按钮
            if (result == DialogResult.Yes)
            {
                try
                {
                    RobotHelper.SendMemberInfo();
                    if (checkBox_SaveMemberBalance.Checked)
                    {
                        // 保留会员Id,备注名,回水比例,余额
                    }
                    else if (checkBox_SaveMemberBaseInfo.Checked)
                    {
                        // 只保留会员Id,备注名,回水比例
                        await DbHelper.FSql.Update<Member>()
                            .Set(a => a.Balance, 0)
                            .Where(a => !string.IsNullOrWhiteSpace(a.Account))
                            .ExecuteAffrowsAsync();
                    }
                    else
                    {
                        // 删除所有数据
                        await DbHelper.FSql.Delete<Member>()
                            .Where(a => !string.IsNullOrWhiteSpace(a.Account))
                            .ExecuteAffrowsAsync();
                    }

                    await DbHelper.FSql.Delete<AddMoney>().Where(a => a.Id > 0).ExecuteAffrowsAsync();
                    await DbHelper.FSql.Delete<SubMoney>().Where(a => a.Id > 0).ExecuteAffrowsAsync();
                    await DbHelper.FSql.Delete<Finance>().Where(a => a.Id > 0).ExecuteAffrowsAsync();

                    await DbHelper.FSql.Delete<BetOrder>().Where(a => a.Id > 0).ExecuteAffrowsAsync();
                    await DbHelper.FSql.Delete<HuiZong>().Where(a => a.Id > 0).ExecuteAffrowsAsync();
                    await DbHelper.FSql.Delete<ReceiveMessage>().Where(a => true).ExecuteAffrowsAsync();
                    await DbHelper.FSql.Delete<Log>().Where(a => a.Id > 0).ExecuteAffrowsAsync();
                    await DbHelper.FSql.Insert<Log>().AppendData(new Log { Type = EnumLogType.机器人, Title = "清空数据", Content = "清空数据成功" }).ExecuteAffrowsAsync();

                    // 设置刷新表格 
                    CommonHelper.NeedToRefreshMemberInfo = true;
                    // this.ShowSuccessTip("清空数据成功");
                }
                catch (Exception ex)
                {
                    await DbHelper.AddLog(EnumLogType.机器人, "ClearData_Error", ex.ToString());
                    await Task.Run(() => { MessageBox.Show(@"清空数据失败", ex.ToString()); });
                }
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "ClearData_Error", ex.ToString());
        }
    }

    /// <summary>
    /// 打开标准时间网站
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void button_ShowTime_Click(object sender, EventArgs e)
    {
        // 使用默认浏览器打开时间戳网站
        try
        {
            // Process.Start("https://biaozhunshijian.bmcx.com/");
            // Process.Start("https://bjtime.org.cn/");
            string url = "https://bjtime.org.cn/"; // 指定要打开的网址
            Process.Start(new ProcessStartInfo(url) { UseShellExecute = true });
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "打开时间戳网站失败", ex.ToString());
        }
    }

    /// <summary>
    /// 设置是否显示回水金额
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void checkBox_ShowReturnCommissionDetail_CheckedChanged(object sender, EventArgs e)
    {
        Setting.Current.显示回水金额 = checkBox_ShowReturnCommissionDetail.Checked ? 1 : 0;
        Setting.Current.Save();
        checkBox_ShowReturnCommissionDetail.ForeColor = checkBox_ShowReturnCommissionDetail.Checked ? Color.Red : Color.Black;
    }

    #endregion

    #region 会员信息表格右键菜单

    /// <summary>
    /// 弹出右键菜单
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void dataGridView_MemberInfo_CellMouseDown(object? sender, DataGridViewCellMouseEventArgs e)
    {
        try
        {
            if (e is { Button: MouseButtons.Right, RowIndex: > 0 })
            {
                // 取消之前选中的行
                dataGridView_MemberInfo.ClearSelection();

                // 选中右键点击的行,取出对应信息
                dataGridView_MemberInfo.Rows[e.RowIndex].Selected = true;
                string account = dataGridView_MemberInfo.Rows[e.RowIndex].Cells[0].Value.ToString()!;
                CommonHelper.CurrentMember = await DbHelper.GetMember(account);

                // 设置菜单选项
                contextMenuStrip_Menu.Items.Clear();
                contextMenuStrip_Menu.Items.Add(@"刷新数据");
                contextMenuStrip_Menu.Items.Add($@"账号:{CommonHelper.CurrentMember.Account}");
                contextMenuStrip_Menu.Items.Add($@"昵称:{CommonHelper.CurrentMember.昵称}");
                contextMenuStrip_Menu.Items.Add($@"备注名:{CommonHelper.CurrentMember.备注名}");
                contextMenuStrip_Menu.Items.Add($@"总上分:{await DbHelper.FSql.Select<AddMoney>().Where(a => a.Account == CommonHelper.CurrentMember.Account && a.Status == EnumBalanceStatus.同意).SumAsync(a => a.Money)}");
                contextMenuStrip_Menu.Items.Add($@"总下分:{await DbHelper.FSql.Select<SubMoney>().Where(a => a.Account == CommonHelper.CurrentMember.Account && a.Status == EnumBalanceStatus.同意).SumAsync(a => a.Money)}");
                contextMenuStrip_Menu.Items.Add("---------------------------");
                contextMenuStrip_Menu.Items.Add("1.操作上分", null, toolStripMenuItem_Click);
                contextMenuStrip_Menu.Items.Add("2.操作下分", null, toolStripMenuItem_Click);
                contextMenuStrip_Menu.Items.Add("3.修改回水比例", null, toolStripMenuItem_Click);
                contextMenuStrip_Menu.Items.Add("4.修改备注名", null, toolStripMenuItem_Click);
                contextMenuStrip_Menu.Items.Add(CommonHelper.CurrentMember.是否假人 ? "5.把他设为真人" : "5.把他设为假人", null, toolStripMenuItem_Click);
                contextMenuStrip_Menu.Items.Add("6.设置拉手上级", null, toolStripMenuItem_Click);

                // 显示ContextMenuStrip
                Point mousePosition = dataGridView_MemberInfo.PointToClient(Cursor.Position);
                contextMenuStrip_Menu.Show(dataGridView_MemberInfo, mousePosition);
            }
        }
        catch (Exception ex)
        {
            // Debug.WriteLine(ex.ToString());
            await DbHelper.AddLog(EnumLogType.机器人, "dataGridView_MemberInfo_右键菜单Error", ex.ToString());
        }
    }

    /// <summary>
    /// 右键菜单选项点击事件
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async void toolStripMenuItem_Click(object? sender, EventArgs e)
    {
        try
        {
            ToolStripMenuItem? toolStripMenuItem = sender as ToolStripMenuItem;
            if (toolStripMenuItem == null || toolStripMenuItem.Text == null)
            {
                return;
            }

            switch (toolStripMenuItem.Text)
            {
                case "刷新数据":
                    CommonHelper.NeedToRefreshMemberInfo = true;
                    break;
                case "1.操作上分":
                    await AddMoneyHandler();
                    CommonHelper.NeedToRefreshMemberInfo = true;
                    break;
                case "2.操作下分":
                    await SubMoneyHandler();
                    CommonHelper.NeedToRefreshMemberInfo = true;
                    break;
                case "3.修改回水比例":
                    await ChangeBackwaterRatioHandler();
                    CommonHelper.NeedToRefreshMemberInfo = true;
                    break;
                case "4.修改备注名":
                    await ChangeRemarkNameHandler();
                    CommonHelper.NeedToRefreshMemberInfo = true;
                    break;
                case "5.把他设为假人":
                    try
                    {
                        // 更新数据库标记
                        await DbHelper.FSql.Update<Member>()
                            .Set(a => a.是否假人, true)
                            .Where(a => a.Account == CommonHelper.CurrentMember.Account)
                            .ExecuteAffrowsAsync();

                        // 记录日志
                        await DbHelper.AddLog(EnumLogType.机器人, "操作会员", $"设置{Ai.中括号左}{CommonHelper.CurrentMember.Account}*{CommonHelper.CurrentMember.备注名}{Ai.中括号右}为{Ai.中括号左}假人{Ai.中括号右}");
                        CommonHelper.NeedToRefreshMemberInfo = true;
                    }
                    catch (Exception ex)
                    {
                        await DbHelper.AddLog(EnumLogType.机器人, "toolStripMenuItem_ClickError", ex.ToString());
                    }

                    break;
                case "5.把他设为真人":
                    try
                    {
                        // 更新数据库标记
                        await DbHelper.FSql.Update<Member>()
                            .Set(a => a.是否假人, false)
                            .Where(a => a.Account == CommonHelper.CurrentMember.Account)
                            .ExecuteAffrowsAsync();

                        // 记录日志
                        await DbHelper.AddLog(EnumLogType.机器人, "操作会员", $"设置{Ai.中括号左}{CommonHelper.CurrentMember.Account}*{CommonHelper.CurrentMember.备注名}{Ai.中括号右}为{Ai.中括号左}真人{Ai.中括号右}");
                        CommonHelper.NeedToRefreshMemberInfo = true;
                    }
                    catch (Exception ex)
                    {
                        await DbHelper.AddLog(EnumLogType.机器人, "toolStripMenuItem_ClickError", ex.ToString());
                    }

                    break;
                case "6.设置拉手上级":
                    await SetParentAccountHandler();
                    break;
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "toolStripMenuItem_ClickError", ex.ToString());
        }
    }

    /// <summary>
    /// 管理员手动上分
    /// </summary>
    private async Task AddMoneyHandler()
    {
        try
        {
            FormInputBox input = new FormInputBox("管理手动上分", "请输入上分值:", "0");
            DialogResult dr = input.ShowDialog();
            if (dr == DialogResult.OK && input.Value.Length > 0)
            {
                decimal inputValue = decimal.Parse(input.Value);
                if (inputValue > 0)
                {
                    // 使用事务操作数据库
                    using DbContext? dbContext = DbHelper.FSql.CreateDbContext();

                    // 创建AddMoney对象
                    AddMoney am = new AddMoney
                    {
                        Account = CommonHelper.CurrentMember.Account,
                        Money = inputValue,
                        FromMsgId = "管理员手动上分",
                        PreTime = DateTime.Now,
                        Status = EnumBalanceStatus.同意
                    };

                    // 保存到数据库
                    await DbHelper.FSql.Insert<AddMoney>()
                        .AppendData(am)
                        .ExecuteIdentityAsync();

                    // 更新会员信息
                    Member tmpMember = await DbHelper.FSql.Select<Member>().Where(a => a.Account == CommonHelper.CurrentMember.Account).FirstAsync();
                    await DbHelper.FSql.Update<Member>()
                        .Set(a => a.Balance, tmpMember.Balance + inputValue)
                        .Where(a => a.Account == CommonHelper.CurrentMember.Account)
                        .ExecuteAffrowsAsync();

                    // 创建财务凭据
                    Finance fn = new Finance
                    {
                        Account = CommonHelper.CurrentMember.Account,
                        变动前 = tmpMember.Balance,
                        变动值 = inputValue,
                        变动后 = tmpMember.Balance + inputValue,
                        凭据 = "管理员手动上分"
                    };

                    // 记录财务凭据
                    await DbHelper.FSql.Insert<Finance>()
                        .AppendData(fn)
                        .ExecuteIdentityAsync();

                    // 提交保存操作
                    await dbContext.SaveChangesAsync();

                    // 发送群消息
                    await ChatHelper.SendGroupMessage($@"上分{Ai.中括号左}{inputValue}{Ai.中括号右}成功" + "\r" + CommonHelper.GetFaceIdMoneyBag() + ":" + Math.Round(tmpMember.Balance + inputValue, 2), CommonHelper.CurrentMember.Account);

                    // 记录日志
                    await DbHelper.AddLog(EnumLogType.机器人, "操作会员", $"管理员手动给{Ai.中括号左}{CommonHelper.CurrentMember.Account}*{CommonHelper.CurrentMember.备注名}{Ai.中括号右}上分{Ai.中括号左}{inputValue}{Ai.中括号右}");
                }
                else if (inputValue < 0)
                {
                    Invoke(() => { MessageBox.Show(@"请输入正确的分数！", @"提示", MessageBoxButtons.OK, MessageBoxIcon.Error); });
                }
            }

            input.Dispose();
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "管理员手动上分Error", ex.ToString());
            Invoke(() => { MessageBox.Show(@"请输入正确的分数！", @"提示", MessageBoxButtons.OK, MessageBoxIcon.Error); });
        }
    }

    /// <summary>
    /// 管理员手动下分
    /// </summary>
    private async Task SubMoneyHandler()
    {
        try
        {
            FormInputBox input = new FormInputBox("管理手动下分", "请输入下分值:", "0");
            DialogResult dr = input.ShowDialog();
            if (dr == DialogResult.OK && input.Value.Length > 0)
            {
                decimal inputValue = decimal.Parse(input.Value);
                if (inputValue > 0)
                {
                    // 获取当前会员信息
                    Member tmpMember = await DbHelper.FSql.Select<Member>().Where(a => a.Account == CommonHelper.CurrentMember.Account).FirstAsync();

                    // 输入的分数大于当前余额
                    if (inputValue > tmpMember.Balance)
                    {
                        await Task.Run(() => { MessageBox.Show(@"输入的分数不能超过用户当前可用分数！", @"提示", MessageBoxButtons.OK, MessageBoxIcon.Error); });
                        return;
                    }

                    // 使用事务操作数据库
                    using DbContext? dbContext = DbHelper.FSql.CreateDbContext();

                    // 创建SubMoney对象
                    SubMoney sm = new SubMoney
                    {
                        Account = CommonHelper.CurrentMember.Account,
                        Money = inputValue,
                        FromMsgId = "管理员手动下分",
                        PreTime = DateTime.Now,
                        Status = EnumBalanceStatus.同意
                    };

                    // 保存到数据库
                    await DbHelper.FSql.Insert<SubMoney>()
                        .AppendData(sm)
                        .ExecuteIdentityAsync();

                    // 创建财务凭据
                    Finance fn = new Finance
                    {
                        Account = CommonHelper.CurrentMember.Account,
                        变动前 = tmpMember.Balance,
                        变动值 = -inputValue,
                        变动后 = tmpMember.Balance - inputValue,
                        凭据 = "管理员手动下分"
                    };

                    // 记录财务凭据
                    await DbHelper.FSql.Insert<Finance>()
                        .AppendData(fn)
                        .ExecuteIdentityAsync();

                    // 修改会员信息
                    tmpMember.Balance -= inputValue;
                    await DbHelper.FSql.Update<Member>()
                        .Set(a => a.Balance, tmpMember.Balance)
                        .Where(a => a.Account == CommonHelper.CurrentMember.Account)
                        .ExecuteAffrowsAsync();

                    // 提交保存操作
                    await dbContext.SaveChangesAsync();

                    // 发送群消息
                    await ChatHelper.SendGroupMessage($@"下分{Ai.中括号左}{inputValue}{Ai.中括号右}成功" + "\r" + CommonHelper.GetFaceIdMoneyBag() + ":" + Math.Round(tmpMember.Balance, 2), CommonHelper.CurrentMember.Account);

                    // 记录日志
                    await DbHelper.AddLog(EnumLogType.机器人, "操作会员", $"管理员手动给{Ai.中括号左}{CommonHelper.CurrentMember.Account}*{CommonHelper.CurrentMember.备注名}{Ai.中括号右}下分{Ai.中括号左}{inputValue}{Ai.中括号右}");
                }
                else if (inputValue < 0)
                {
                    Invoke(() => { MessageBox.Show(@"请输入正确的分数！", @"提示", MessageBoxButtons.OK, MessageBoxIcon.Error); });
                }
            }

            input.Dispose();
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "管理员手动下分Error", ex.ToString());
            Invoke(() => { MessageBox.Show(@"请输入正确的分数！", @"提示", MessageBoxButtons.OK, MessageBoxIcon.Error); });
        }
    }

    /// <summary>
    /// 修改回水比例
    /// </summary>
    private async Task ChangeBackwaterRatioHandler()
    {
        try
        {
            FormInputBox input = new FormInputBox("修改回水比例", "请输入回水比例:", CommonHelper.CurrentMember.回水比例.ToString(CultureInfo.InvariantCulture));
            DialogResult dr = input.ShowDialog();
            if (dr == DialogResult.OK && input.Value.Length > 0)
            {
                decimal value = Convert.ToDecimal(input.Value);

                // 更新数据库
                await DbHelper.FSql.Update<Member>()
                    .Set(a => a.回水比例, value)
                    .Where(a => a.Account == CommonHelper.CurrentMember.Account)
                    .ExecuteAffrowsAsync();

                // 记录日志
                await DbHelper.AddLog(EnumLogType.平台, "操作会员", $"修改{Ai.中括号左}{CommonHelper.CurrentMember.Account}*{CommonHelper.CurrentMember.备注名}{Ai.中括号右}的回水比例为{Ai.中括号左}{value}{Ai.中括号右}");
            }

            input.Dispose();
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "toolStripMenuItem_Click_修改回水比例Error", ex.ToString());
        }
    }

    /// <summary>
    /// 修改备注名
    /// </summary>
    private async Task ChangeRemarkNameHandler()
    {
        try
        {
            FormInputBox input = new FormInputBox("修改备注名", "请输入备注名:", CommonHelper.CurrentMember.备注名);
            DialogResult dr = input.ShowDialog();
            if (dr == DialogResult.OK && input.Value.Length > 0)
            {
                string value = input.Value;

                // 更新数据库
                await DbHelper.FSql.Update<Member>()
                    .Set(a => a.备注名, value)
                    .Where(a => a.Account == CommonHelper.CurrentMember.Account)
                    .ExecuteAffrowsAsync();

                // 记录日志
                await DbHelper.AddLog(EnumLogType.平台, "操作会员", $"修改{Ai.中括号左}{CommonHelper.CurrentMember.Account}*{CommonHelper.CurrentMember.备注名}{Ai.中括号右}的备注名为{Ai.中括号左}{value}{Ai.中括号右}");
            }

            input.Dispose();
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "toolStripMenuItem_Click_修改回水比例Error", ex.ToString());
        }
    }

    /// <summary>
    /// 设置拉手上级
    /// </summary>
    private async Task SetParentAccountHandler()
    {
        try
        {
            FormSetParentAccount input = new FormSetParentAccount();
            DialogResult dr = input.ShowDialog();
            // if (dr == DialogResult.OK && input.Value.Length > 0)
            // {
            //     decimal inputValue = decimal.Parse(input.Value);
            //     if (inputValue > 0)
            //     {
            //         // 使用事务操作数据库
            //         using DbContext? dbContext = DbHelper.FSql.CreateDbContext();
            //
            //         // 创建AddMoney对象
            //         AddMoney am = new AddMoney
            //         {
            //             Account = CommonHelper.CurrentMember.Account,
            //             Money = inputValue,
            //             FromMsgId = "管理员手动上分",
            //             PreTime = DateTime.Now,
            //             Status = EnumBalanceStatus.同意
            //         };
            //
            //         // 保存到数据库
            //         await DbHelper.FSql.Insert<AddMoney>()
            //             .AppendData(am)
            //             .ExecuteIdentityAsync();
            //
            //         // 更新会员信息
            //         Member tmpMember = await DbHelper.FSql.Select<Member>().Where(a => a.Account == CommonHelper.CurrentMember.Account).FirstAsync();
            //         await DbHelper.FSql.Update<Member>()
            //             .Set(a => a.Balance, tmpMember.Balance + inputValue)
            //             .Where(a => a.Account == CommonHelper.CurrentMember.Account)
            //             .ExecuteAffrowsAsync();
            //
            //         // 创建财务凭据
            //         Finance fn = new Finance
            //         {
            //             Account = CommonHelper.CurrentMember.Account,
            //             变动前 = tmpMember.Balance,
            //             变动值 = inputValue,
            //             变动后 = tmpMember.Balance + inputValue,
            //             凭据 = "管理员手动上分"
            //         };
            //
            //         // 记录财务凭据
            //         await DbHelper.FSql.Insert<Finance>()
            //             .AppendData(fn)
            //             .ExecuteIdentityAsync();
            //
            //         // 提交保存操作
            //         await dbContext.SaveChangesAsync();
            //
            //         // 发送群消息
            //         await ChatHelper.SendGroupMessage($@"上分{Ai.中括号左}{inputValue}{Ai.中括号右}成功" + "\r" + CommonHelper.GetFaceIdMoneyBag() + ":" + Math.Round(tmpMember.Balance + inputValue, 2), CommonHelper.CurrentMember.Account);
            //
            //         // 记录日志
            //         await DbHelper.AddLog(EnumLogType.机器人, "操作会员", $"管理员手动给{Ai.中括号左}{CommonHelper.CurrentMember.Account}*{CommonHelper.CurrentMember.备注名}{Ai.中括号右}上分{Ai.中括号左}{inputValue}{Ai.中括号右}");
            //     }
            //     else if (inputValue < 0)
            //     {
            //         Invoke(() => { MessageBox.Show(@"请输入正确的分数！", @"提示", MessageBoxButtons.OK, MessageBoxIcon.Error); });
            //     }
            // }

            input.Dispose();
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "管理员手动上分Error", ex.ToString());
            Invoke(() => { MessageBox.Show(@"请输入正确的分数！", @"提示", MessageBoxButtons.OK, MessageBoxIcon.Error); });
        }
    }

    #endregion
}