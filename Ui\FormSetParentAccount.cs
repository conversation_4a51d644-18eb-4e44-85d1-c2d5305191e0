﻿using AiHelper;
using Robot.Enum;
using Robot.Helper;
using Robot.Models;

namespace Robot.Ui;

public partial class FormSetParentAccount : Form
{
    #region 公共属性

    /// <summary>
    /// 用户输入的值
    ///
    /// 功能说明：
    /// 1. 在窗体显示前可以设置默认值
    /// 2. 用户点击确定后，该属性包含用户输入的最终内容
    /// 3. 用户点击取消时，该属性值不会被更新
    ///
    /// 使用方式：
    /// - 设置默认值：inputBox.Value = "默认内容";
    /// - 获取输入值：string result = inputBox.Value;
    /// </summary>
    private string Title { get; set; } = string.Empty;

    public string 拉手上级 { get; private set; } = string.Empty;
    public decimal 返利比例 { get; set; }

    #endregion

    public FormSetParentAccount()
    {
        InitializeComponent();
    }

    private async void FormSetParentAccount_Load(object sender, EventArgs e)
    {
        try
        {
            await AddMemberToComboBox(comboBox_Member);
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, @"AddMemberToComboBoxError", ex.ToString());
        }
    }

    private async Task AddMemberToComboBox(ComboBox comboBox)
    {
        try
        {
            List<Member> memberList = await DbHelper.FSql.Select<Member>().ToListAsync();
            comboBox.Items.Clear();
            comboBox.Items.Add("请选择拉手上级");
            foreach (Member member in memberList)
            {
                comboBox.Items.Add($"{Ai.中括号左}{member.Account}{Ai.中括号右}{member.昵称}");
            }

            comboBox.SelectedIndex = 0;
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "AddMemberToComboBoxError", ex.ToString());
        }
    }

    private async void button_Sure_Click(object sender, EventArgs e)
    {
        try
        {
            if (comboBox_Member.SelectedIndex > 0)
            {
                拉手上级 = Ai.GetTextMiddle(comboBox_Member.Text, Ai.中括号左, Ai.中括号右);
                try
                {
                    返利比例 = Convert.ToDecimal(textBox_返利比例.Text.Trim());

                    // 判断返利比例是否在0-2.5之间
                    if (返利比例 is < 0 or > (decimal)2.5)
                    {
                        MessageBox.Show(@"返利比例输入错误，请重新输入！参考范围为0-2.5之间");
                        return;
                    }

                    DialogResult = DialogResult.OK;
                    Close();
                }
                catch (Exception ex)
                {
                    await DbHelper.AddLog(EnumLogType.机器人, "Convert.ToDecimalError", ex.ToString());
                    MessageBox.Show(@"设置返利比例输入错误，请重新输入！");
                }
            }
        }
        catch (Exception ex)
        {
            await DbHelper.AddLog(EnumLogType.机器人, "button_Sure_ClickError", ex.ToString());
        }
    }
}