C:\OK\SolutionRobot\Robot\bin\Debug\net8.0-windows\Robot.exe
C:\OK\SolutionRobot\Robot\bin\Debug\net8.0-windows\Robot.deps.json
C:\OK\SolutionRobot\Robot\bin\Debug\net8.0-windows\Robot.runtimeconfig.json
C:\OK\SolutionRobot\Robot\bin\Debug\net8.0-windows\Robot.dll
C:\OK\SolutionRobot\Robot\bin\Debug\net8.0-windows\Robot.pdb
C:\OK\SolutionRobot\Robot\bin\Debug\net8.0-windows\runtimes\win-arm64\native\WebView2Loader.dll
C:\OK\SolutionRobot\Robot\bin\Debug\net8.0-windows\runtimes\win-x64\native\WebView2Loader.dll
C:\OK\SolutionRobot\Robot\bin\Debug\net8.0-windows\runtimes\win-x86\native\WebView2Loader.dll
C:\OK\SolutionRobot\Robot\bin\Debug\net8.0-windows\runtimes\linux-x64\native\SQLite.Interop.dll
C:\OK\SolutionRobot\Robot\bin\Debug\net8.0-windows\runtimes\osx-x64\native\SQLite.Interop.dll
C:\OK\SolutionRobot\Robot\bin\Debug\net8.0-windows\runtimes\win-x64\native\SQLite.Interop.dll
C:\OK\SolutionRobot\Robot\bin\Debug\net8.0-windows\runtimes\win-x86\native\SQLite.Interop.dll
C:\OK\SolutionRobot\Robot\bin\Debug\net8.0-windows\runtimes\win\lib\net8.0\System.Management.dll
C:\OK\SolutionRobot\Robot\bin\Debug\net8.0-windows\AiHelper.dll.config
C:\OK\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.csproj.AssemblyReference.cache
C:\OK\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.FormAlertMessage.resources
C:\OK\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.FormIndex.resources
C:\OK\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.FormInputBox.resources
C:\OK\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.FormMain.resources
C:\OK\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.csproj.GenerateResource.cache
C:\OK\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.GeneratedMSBuildEditorConfig.editorconfig
C:\OK\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.AssemblyInfoInputs.cache
C:\OK\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.AssemblyInfo.cs
C:\OK\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.csproj.CoreCompileInputs.cache
C:\OK\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.csproj.Fody.CopyLocal.cache
C:\OK\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.csproj.Fody.RuntimeCopyLocal.cache
C:\OK\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.csproj.Up2Date
C:\OK\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.dll
C:\OK\SolutionRobot\Robot\obj\Debug\net8.0-windows\refint\Robot.dll
C:\OK\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.pdb
C:\OK\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.genruntimeconfig.cache
C:\OK\SolutionRobot\Robot\obj\Debug\net8.0-windows\ref\Robot.dll
D:\Zhou\SolutionRobot\Robot\bin\Debug\net8.0-windows\Robot.exe
D:\Zhou\SolutionRobot\Robot\bin\Debug\net8.0-windows\Robot.deps.json
D:\Zhou\SolutionRobot\Robot\bin\Debug\net8.0-windows\Robot.runtimeconfig.json
D:\Zhou\SolutionRobot\Robot\bin\Debug\net8.0-windows\Robot.dll
D:\Zhou\SolutionRobot\Robot\bin\Debug\net8.0-windows\Robot.pdb
D:\Zhou\SolutionRobot\Robot\bin\Debug\net8.0-windows\runtimes\win-arm64\native\WebView2Loader.dll
D:\Zhou\SolutionRobot\Robot\bin\Debug\net8.0-windows\runtimes\win-x64\native\WebView2Loader.dll
D:\Zhou\SolutionRobot\Robot\bin\Debug\net8.0-windows\runtimes\win-x86\native\WebView2Loader.dll
D:\Zhou\SolutionRobot\Robot\bin\Debug\net8.0-windows\runtimes\linux-x64\native\SQLite.Interop.dll
D:\Zhou\SolutionRobot\Robot\bin\Debug\net8.0-windows\runtimes\osx-x64\native\SQLite.Interop.dll
D:\Zhou\SolutionRobot\Robot\bin\Debug\net8.0-windows\runtimes\win-x64\native\SQLite.Interop.dll
D:\Zhou\SolutionRobot\Robot\bin\Debug\net8.0-windows\runtimes\win-x86\native\SQLite.Interop.dll
D:\Zhou\SolutionRobot\Robot\bin\Debug\net8.0-windows\runtimes\win\lib\net8.0\System.Management.dll
D:\Zhou\SolutionRobot\Robot\bin\Debug\net8.0-windows\AiHelper.dll.config
D:\Zhou\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.csproj.AssemblyReference.cache
D:\Zhou\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.FormAlertMessage.resources
D:\Zhou\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.FormIndex.resources
D:\Zhou\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.FormInputBox.resources
D:\Zhou\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.FormMain.resources
D:\Zhou\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.csproj.GenerateResource.cache
D:\Zhou\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.GeneratedMSBuildEditorConfig.editorconfig
D:\Zhou\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.AssemblyInfoInputs.cache
D:\Zhou\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.AssemblyInfo.cs
D:\Zhou\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.csproj.CoreCompileInputs.cache
D:\Zhou\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.csproj.Fody.CopyLocal.cache
D:\Zhou\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.csproj.Fody.RuntimeCopyLocal.cache
D:\Zhou\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.csproj.Up2Date
D:\Zhou\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.dll
D:\Zhou\SolutionRobot\Robot\obj\Debug\net8.0-windows\refint\Robot.dll
D:\Zhou\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.pdb
D:\Zhou\SolutionRobot\Robot\obj\Debug\net8.0-windows\Robot.genruntimeconfig.cache
D:\Zhou\SolutionRobot\Robot\obj\Debug\net8.0-windows\ref\Robot.dll
